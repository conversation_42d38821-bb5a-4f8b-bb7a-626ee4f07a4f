
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-09-17T14:53:24+0200",
 "dirty": false,
 "error": null,
 "full-revisionid": "b8cd1e273078dfd023916cceaae247158e24c15e",
 "version": "0.62.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
