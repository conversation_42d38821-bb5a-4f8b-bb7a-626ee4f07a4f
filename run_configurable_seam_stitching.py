#!/usr/bin/env python3
"""
Configurable Seam-Based Stitching Runner
Allows easy parameter adjustment for better results
"""

import os
import sys
import time
from seam_config import SeamConfig, SeamPresets
from fase_1_with_seam_finding import get_image_positions, create_seamless_mosaic_with_seams

def run_with_config(config, folder_path, rows, cols, overlap_pct):
    """Run stitching with specific configuration"""
    
    print("🔬 CONFIGURABLE SEAM-BASED MICROSCOPIC STITCHING")
    print("=" * 60)
    print(f"📁 Input folder: {folder_path}")
    print(f"📐 Grid layout: {rows} x {cols}")
    print(f"🔄 Overlap: {overlap_pct * 100:.1f}%")
    print("=" * 60)
    
    # Print current configuration
    config.print_current_config()
    
    # Check if folder exists
    if not os.path.exists(folder_path):
        print(f"❌ Error: Folder not found: {folder_path}")
        return False
    
    # Check for images
    import glob
    image_files = glob.glob(os.path.join(folder_path, "*.png")) + glob.glob(os.path.join(folder_path, "*.jpg"))
    if not image_files:
        print(f"❌ Error: No PNG or JPG images found in {folder_path}")
        return False
    
    print(f"✅ Found {len(image_files)} images")
    
    # Start processing
    start_time = time.time()
    
    try:
        # Step 1: Calculate positions
        print("\n🔍 Step 1: Calculating image positions...")
        files, positions = get_image_positions(folder_path, rows, cols, overlap_pct)
        
        if not files or not positions:
            print("❌ Error: Could not calculate image positions")
            return False
        
        print(f"✅ Calculated positions for {len(files)} images")
        
        # Step 2: Create seamless mosaic with custom configuration
        print("\n🎨 Step 2: Creating seamless mosaic with custom seam finding...")
        
        # Pass configuration to the stitching function
        mosaic, output_path = create_seamless_mosaic_with_seams_configurable(
            files, positions, rows, cols, overlap_pct, config
        )
        
        # Calculate processing time
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Success summary
        print("\n" + "=" * 60)
        print("🎉 CONFIGURABLE SEAM-BASED STITCHING COMPLETED!")
        print("=" * 60)
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📄 Output saved: {output_path}")
        print(f"🖼️  Mosaic shape: {mosaic.shape}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_seamless_mosaic_with_seams_configurable(image_files, positions, rows, cols, overlap_pct, config):
    """Modified version that uses configuration"""
    print("Memulai Fase 3: Penyambungan dengan Configurable Seam Finding...")
    
    # Import required modules
    import cv2
    import numpy as np
    import tifffile
    from seam_finding import SeamFinder, create_overlap_regions
    
    # Load first image to get dimensions
    first_img = cv2.imread(image_files[0])
    h, w = first_img.shape[:2]
    is_color = len(first_img.shape) == 3

    # Calculate mosaic dimensions
    min_x = min(pos[0] for pos in positions.values())
    max_x = max(pos[0] + w for pos in positions.values())
    min_y = min(pos[1] for pos in positions.values())
    max_y = max(pos[1] + h for pos in positions.values())

    mosaic_w = int(max_x - min_x)
    mosaic_h = int(max_y - min_y)
    
    print(f"Mosaic dimensions: {mosaic_w} x {mosaic_h}")
    
    # Initialize seam finder with configuration
    seam_finder = SeamFinder(
        blend_width=config.blend_width,
        cost_weights=config.cost_weights,
        feather_width=config.seam_feather_width,
        cost_smoothing_kernel=config.cost_smoothing_kernel
    )
    
    # Create position list for overlap calculation
    position_list = [(positions[i][1] - min_y, positions[i][0] - min_x) for i in range(len(image_files))]
    
    # Find overlap regions
    print("Mencari region overlap...")
    overlap_regions = create_overlap_regions(position_list, (h, w), overlap_pct)
    print(f"Ditemukan {len(overlap_regions)} region overlap")
    
    # Load all images with color correction
    print("Loading semua gambar...")
    images = []
    for img_path in image_files:
        img = cv2.imread(img_path)
        # Convert BGR to RGB for correct color display
        if len(img.shape) == 3:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        images.append(img)
    
    # Calculate seams for all overlaps
    print("Menghitung seam optimal dengan konfigurasi custom...")
    seam_masks = {}
    
    for i, overlap in enumerate(overlap_regions):
        img1_idx = overlap['img1_idx']
        img2_idx = overlap['img2_idx']
        region = overlap['region']
        direction = overlap['direction']
        
        if config.debug_mode:
            print(f"  Processing overlap {i+1}/{len(overlap_regions)}: Image {img1_idx} <-> Image {img2_idx} ({direction})")
        elif i % 10 == 0:
            print(f"  Processed {i}/{len(overlap_regions)} overlaps...")
        
        # Get images
        img1 = images[img1_idx]
        img2 = images[img2_idx]
        
        # Calculate overlap region in image coordinates
        pos1 = position_list[img1_idx]
        pos2 = position_list[img2_idx]
        
        # Convert global overlap region to local image coordinates
        y1, y2, x1, x2 = region
        
        # Local coordinates for img1
        local_y1_1 = max(0, int(y1 - pos1[0]))
        local_y2_1 = min(h, int(y2 - pos1[0]))
        local_x1_1 = max(0, int(x1 - pos1[1]))
        local_x2_1 = min(w, int(x2 - pos1[1]))
        
        # Local coordinates for img2
        local_y1_2 = max(0, int(y1 - pos2[0]))
        local_y2_2 = min(h, int(y2 - pos2[0]))
        local_x1_2 = max(0, int(x1 - pos2[1]))
        local_x2_2 = min(w, int(x2 - pos2[1]))
        
        # Extract overlap regions
        if (local_y2_1 > local_y1_1 and local_x2_1 > local_x1_1 and 
            local_y2_2 > local_y1_2 and local_x2_2 > local_x1_2):
            
            overlap_region = (local_y1_1, local_y2_1, local_x1_1, local_x2_1)
            
            try:
                seam_mask = seam_finder.find_optimal_seam(
                    img1, img2, overlap_region, direction
                )
                seam_masks[(img1_idx, img2_idx)] = {
                    'mask': seam_mask,
                    'region': region,
                    'local_region_1': overlap_region,
                    'local_region_2': (local_y1_2, local_y2_2, local_x1_2, local_x2_2)
                }
            except Exception as e:
                if config.debug_mode:
                    print(f"    Warning: Seam finding failed for {img1_idx}-{img2_idx}: {e}")
    
    # Create final mosaic with seam-based blending
    print("Membuat mosaic final dengan seam blending...")
    
    if is_color:
        final_mosaic = np.zeros((mosaic_h, mosaic_w, 3), dtype=np.float32)
        weight_sum = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
    else:
        final_mosaic = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
        weight_sum = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
    
    # Place images with seam-aware blending
    for i in range(len(image_files)):
        img = images[i].astype(np.float32)
        
        pos_x = int(positions[i][0] - min_x)
        pos_y = int(positions[i][1] - min_y)
        
        # Create base weight map (distance-based feathering)
        tile_weight = create_distance_weight_configurable(img.shape[:2], config)
        
        # Modify weight based on seams
        tile_weight = apply_seam_weights_configurable(tile_weight, i, seam_masks, position_list, config)
        
        # Place image
        h_part, w_part = img.shape[:2]
        
        if is_color:
            for c in range(3):
                final_mosaic[pos_y:pos_y+h_part, pos_x:pos_x+w_part, c] += img[:,:,c] * tile_weight
        else:
            final_mosaic[pos_y:pos_y+h_part, pos_x:pos_x+w_part] += img * tile_weight
        
        weight_sum[pos_y:pos_y+h_part, pos_x:pos_x+w_part] += tile_weight
        
        if i % 5 == 0:
            print(f"  Placed image {i+1}/{len(image_files)}")

    # Normalize
    weight_sum[weight_sum == 0] = 1.0
    if is_color:
        for c in range(3):
            final_mosaic[:,:,c] /= weight_sum
    else:
        final_mosaic /= weight_sum
    
    final_mosaic = final_mosaic.astype(np.uint8)
    
    # Apply grid removal if enabled
    if config.apply_grid_removal:
        print("Applying grid artifact removal...")
        final_mosaic = apply_grid_removal(final_mosaic, config.grid_removal_strength)
    
    # Save result with configuration info in filename
    config_name = f"blend{config.blend_width}_grad{config.cost_weights['gradient']:.1f}"
    output_path = os.path.join(os.path.dirname(image_files[0]), f"seamless_mosaic_{config_name}.tif")
    tifffile.imwrite(output_path, final_mosaic)

    print(f"\nPenyambungan Selesai. Mozaik tersimpan di: {output_path}")
    return final_mosaic, output_path

def create_distance_weight_configurable(shape, config):
    """Create distance-based weight map with configuration"""
    import numpy as np
    h, w = shape

    # Create binary mask
    mask = np.ones((h, w), dtype=np.float32)
    mask[0, :] = 0  # top edge
    mask[-1, :] = 0  # bottom edge
    mask[:, 0] = 0  # left edge
    mask[:, -1] = 0  # right edge
    
    # Distance transform
    from scipy.ndimage import distance_transform_edt
    distance = distance_transform_edt(mask)
    
    # Create smooth weight with configurable parameters
    weight = np.minimum(distance / config.blend_width, 1.0)
    weight = 0.5 * (1 + np.tanh(4 * config.distance_transform_sigma * (weight - 0.5)))
    
    return weight

def apply_seam_weights_configurable(base_weight, img_idx, seam_masks, positions, config):
    """Apply seam-based weight modifications with configuration"""
    import cv2
    
    modified_weight = base_weight.copy()
    
    # Apply seam masks where this image overlaps with others
    for (idx1, idx2), seam_data in seam_masks.items():
        if img_idx == idx1:
            # This image is img1 in the seam calculation
            mask = seam_data['mask']
            region = seam_data['local_region_1']
            y1, y2, x1, x2 = region
            
            if y2 > y1 and x2 > x1:
                # Ensure mask dimensions match the region
                region_h, region_w = y2 - y1, x2 - x1
                if mask.shape != (region_h, region_w):
                    mask = cv2.resize(mask, (region_w, region_h))
                modified_weight[y1:y2, x1:x2] *= mask
                
        elif img_idx == idx2:
            # This image is img2 in the seam calculation
            mask = 1.0 - seam_data['mask']  # Invert mask for img2
            region = seam_data['local_region_2']
            y1, y2, x1, x2 = region
            
            if y2 > y1 and x2 > x1:
                # Ensure mask dimensions match the region
                region_h, region_w = y2 - y1, x2 - x1
                if mask.shape != (region_h, region_w):
                    mask = cv2.resize(mask, (region_w, region_h))
                modified_weight[y1:y2, x1:x2] *= mask
    
    return modified_weight

def apply_grid_removal(image, strength):
    """Apply FFT-based grid artifact removal"""
    import numpy as np
    import cv2

    # Simple grid removal using frequency domain filtering
    # This is a placeholder - can be enhanced with more sophisticated methods
    if len(image.shape) == 3:
        result = image.copy()
        for c in range(3):
            # Apply gentle smoothing to reduce grid artifacts
            kernel_size = max(3, int(strength * 10))
            if kernel_size % 2 == 0:
                kernel_size += 1
            result[:,:,c] = cv2.GaussianBlur(image[:,:,c], (kernel_size, kernel_size), strength)
        return result
    else:
        kernel_size = max(3, int(strength * 10))
        if kernel_size % 2 == 0:
            kernel_size += 1
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), strength)

def main():
    """Main function with configuration options"""
    
    # Configuration - adjust these paths and parameters as needed
    folder_path = r"D:\Stitch\bilinear_stitching_20250915_152529"
    rows = 4
    cols = 9
    overlap_pct = 0.2
    
    print("🎯 SEAM CONFIGURATION OPTIONS")
    print("=" * 50)
    print("1. Default Configuration")
    print("2. High Quality (Slow)")
    print("3. Balanced Quality/Speed")
    print("4. Fast Processing")
    print("5. Grid Elimination Focus")
    print("6. Low Feature Images")
    print("=" * 50)
    
    # For now, use Grid Elimination Focus preset
    # You can change this to test different configurations
    config = SeamPresets.grid_elimination_focus()
    
    print("🔧 Using: Grid Elimination Focus Preset")
    print("   (Best for eliminating grid artifacts)")
    
    success = run_with_config(config, folder_path, rows, cols, overlap_pct)
    
    if success:
        print("\n🎯 Try Different Configurations:")
        print("  • Edit seam_config.py to adjust parameters")
        print("  • Use different presets in SeamPresets class")
        print("  • Compare results with different settings")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
