#!/usr/bin/env python3
"""
Enhanced Image Stitching Pipeline with Global Optimization
Implements robust computer vision pipeline for seamless microscopy tile stitching
with automatic fallback methods for featureless regions
"""

import os
import glob
import numpy as np
import cv2
import skimage.io
from skimage import exposure, filters
from skimage.registration import phase_cross_correlation
from scipy.spatial.distance import cdist
from scipy.optimize import least_squares
import time
from collections import defaultdict

class EnhancedImageStitcher:
    def __init__(self):
        self.images = []
        self.image_paths = []
        self.keypoints = []
        self.descriptors = []
        self.pairwise_transforms = []
        self.transform_methods = []  # Track which method was used for each pair
        self.global_transforms = []
        self.connectivity_graph = defaultdict(list)

        # Feature detector - Using ORB as primary (more robust for microscopy)
        self.detector = cv2.ORB_create(nfeatures=1000, scaleFactor=1.2, nlevels=8)

        # Matcher
        self.matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)

        # Thresholds
        self.min_matches_threshold = 15  # Minimum matches for feature-based method
        self.ransac_threshold = 5.0
        self.ransac_confidence = 0.99

        print("🔬 Enhanced Image Stitching Pipeline with Global Optimization")
        print("📊 Primary: ORB feature detection | Fallback: Phase Correlation")
        print(f"🎯 Minimum matches threshold: {self.min_matches_threshold}")
    
    def load_images(self, folder_path):
        """Step 1: Input & Preprocessing"""
        print("\n" + "="*50)
        print("📁 STEP 1: Loading and Preprocessing Images")
        print("="*50)

        # Get image files
        image_files = glob.glob(os.path.join(folder_path, "*.png"))
        image_files.extend(glob.glob(os.path.join(folder_path, "*.jpg")))
        image_files.extend(glob.glob(os.path.join(folder_path, "*.tif")))
        image_files.sort()

        if not image_files:
            raise ValueError("No images found in folder!")

        print(f"Found {len(image_files)} images")

        self.images = []
        self.image_paths = image_files

        for i, img_path in enumerate(image_files):
            # Load image
            img = cv2.imread(img_path)
            if img is None:
                img = skimage.io.imread(img_path)
                if img.ndim == 3:
                    img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

            # Preprocessing
            if img.ndim == 3:
                # Resize if too large (for faster processing)
                h, w = img.shape[:2]
                if max(h, w) > 2000:
                    scale = 2000 / max(h, w)
                    new_w, new_h = int(w * scale), int(h * scale)
                    img = cv2.resize(img, (new_w, new_h))
                    print(f"Image {i}: Resized to {new_w}x{new_h}")

            self.images.append(img)

            if i % 10 == 0:
                print(f"Loaded image {i+1}/{len(image_files)}")

        print(f"✅ Loaded {len(self.images)} images successfully")
        return len(self.images)
    
    def detect_features(self):
        """Step 2: Feature Detection & Extraction"""
        print("\n" + "="*50)
        print("🔍 STEP 2: Feature Detection & Extraction")
        print("="*50)

        self.keypoints = []
        self.descriptors = []

        for i, img in enumerate(self.images):
            # Convert to grayscale for feature detection
            if img.ndim == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img

            # Enhance contrast for better feature detection
            gray = exposure.equalize_adapthist(gray, clip_limit=0.02)
            gray = (gray * 255).astype(np.uint8)

            # Detect keypoints and compute descriptors
            kp, desc = self.detector.detectAndCompute(gray, None)

            self.keypoints.append(kp)
            self.descriptors.append(desc)

            print(f"Image {i}: Found {len(kp)} keypoints")

            if i % 10 == 0 and i > 0:
                print(f"Processed {i+1}/{len(self.images)} images")

        total_features = sum(len(kp) for kp in self.keypoints)
        print(f"✅ Total features detected: {total_features}")

        return total_features

    def compute_phase_correlation_transform(self, img1, img2):
        """Fallback method: Compute translation using phase correlation"""
        # Convert to grayscale if needed
        if img1.ndim == 3:
            gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        else:
            gray1 = img1

        if img2.ndim == 3:
            gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        else:
            gray2 = img2

        # Normalize images
        gray1 = gray1.astype(np.float64) / 255.0
        gray2 = gray2.astype(np.float64) / 255.0

        try:
            # Compute phase correlation
            shift, error, diffphase = phase_cross_correlation(gray1, gray2, upsample_factor=10)

            # Create translation matrix
            H = np.eye(3, dtype=np.float64)
            H[0, 2] = shift[1]  # x translation
            H[1, 2] = shift[0]  # y translation

            return H, error

        except Exception as e:
            print(f"Phase correlation failed: {e}")
            return None, 0.0
    
    def compute_pairwise_transforms(self):
        """Step 3: Compute Pairwise Transformations with Fallback Methods"""
        print("\n" + "="*50)
        print("🔗 STEP 3: Pairwise Transformation Computation")
        print("="*50)

        self.pairwise_transforms = []
        self.transform_methods = []

        # Process adjacent image pairs (assuming sequential order)
        for i in range(len(self.images) - 1):
            print(f"\nProcessing pair {i}-{i+1}:")

            # Method 1: Feature-based approach (Primary)
            transform, method_used = self._try_feature_based_transform(i, i+1)

            # Method 2: Phase correlation fallback
            if transform is None:
                print(f"  🔄 Feature-based method failed, trying phase correlation...")
                transform, method_used = self._try_phase_correlation_transform(i, i+1)

            # Store results
            self.pairwise_transforms.append(transform)
            self.transform_methods.append(method_used)

            # Add to connectivity graph if successful
            if transform is not None:
                self.connectivity_graph[i].append(i+1)
                self.connectivity_graph[i+1].append(i)

            print(f"  ✅ Method used: {method_used}")

        # Summary
        successful_transforms = sum(1 for t in self.pairwise_transforms if t is not None)
        feature_based_count = sum(1 for m in self.transform_methods if m == "Feature-based (ORB)")
        phase_corr_count = sum(1 for m in self.transform_methods if m == "Phase Correlation")

        print(f"\n📊 Transformation Summary:")
        print(f"  ✅ Successful transforms: {successful_transforms}/{len(self.pairwise_transforms)}")
        print(f"  🎯 Feature-based (ORB): {feature_based_count}")
        print(f"  📐 Phase Correlation: {phase_corr_count}")

        return successful_transforms

    def _try_feature_based_transform(self, idx1, idx2):
        """Try to compute transform using feature matching"""
        # Check if descriptors exist
        if (self.descriptors[idx1] is None or self.descriptors[idx2] is None or
            len(self.descriptors[idx1]) == 0 or len(self.descriptors[idx2]) == 0):
            print(f"  ❌ No descriptors available")
            return None, "Failed - No descriptors"

        try:
            # Match features using Hamming distance for ORB
            matches = self.matcher.match(self.descriptors[idx1], self.descriptors[idx2])

            # Sort matches by distance
            matches = sorted(matches, key=lambda x: x.distance)

            print(f"  🔍 Found {len(matches)} raw matches")

            # Filter matches - keep only the best ones
            if len(matches) < self.min_matches_threshold:
                print(f"  ❌ Insufficient matches ({len(matches)} < {self.min_matches_threshold})")
                return None, "Failed - Insufficient matches"

            # Take top matches
            good_matches = matches[:min(len(matches), 100)]

            # Extract matched points
            src_pts = np.float32([self.keypoints[idx1][m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([self.keypoints[idx2][m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

            # Estimate homography with RANSAC
            H, mask = cv2.findHomography(src_pts, dst_pts,
                                       cv2.RANSAC,
                                       ransacReprojThreshold=self.ransac_threshold,
                                       confidence=self.ransac_confidence)

            if H is not None:
                inliers = np.sum(mask)
                print(f"  ✅ Homography found: {inliers}/{len(good_matches)} inliers")
                return H, "Feature-based (ORB)"
            else:
                print(f"  ❌ Homography estimation failed")
                return None, "Failed - Homography estimation"

        except Exception as e:
            print(f"  ❌ Feature matching error: {e}")
            return None, f"Failed - {str(e)}"

    def _try_phase_correlation_transform(self, idx1, idx2):
        """Try to compute transform using phase correlation"""
        try:
            H, error = self.compute_phase_correlation_transform(self.images[idx1], self.images[idx2])

            if H is not None:
                print(f"  ✅ Phase correlation successful (error: {error:.4f})")
                return H, "Phase Correlation"
            else:
                print(f"  ❌ Phase correlation failed")
                return None, "Failed - Phase correlation"

        except Exception as e:
            print(f"  ❌ Phase correlation error: {e}")
            return None, f"Failed - {str(e)}"

    def global_bundle_adjustment(self):
        """Step 4: Global Bundle Adjustment for Optimal Alignment"""
        print("\n" + "="*50)
        print("🌐 STEP 4: Global Bundle Adjustment")
        print("="*50)

        if not self.pairwise_transforms:
            print("❌ No pairwise transforms available for global optimization")
            return 0

        # Initialize global transforms
        self.global_transforms = []
        n_images = len(self.images)

        # Method 1: Simple accumulation (baseline)
        print("🔄 Computing initial global transforms...")
        cumulative_H = np.eye(3, dtype=np.float64)
        self.global_transforms.append(cumulative_H.copy())

        for i, H in enumerate(self.pairwise_transforms):
            if H is not None:
                cumulative_H = np.dot(cumulative_H, H)
            # Always append a transform (use previous if current failed)
            self.global_transforms.append(cumulative_H.copy())

        # Method 2: Iterative refinement to minimize global error
        print("🔄 Performing iterative refinement...")
        self._refine_global_transforms()

        print(f"✅ Global transforms computed for {len(self.global_transforms)} images")
        return len(self.global_transforms)

    def _refine_global_transforms(self):
        """Refine global transforms to minimize accumulated error"""
        max_iterations = 5
        convergence_threshold = 1e-6

        for iteration in range(max_iterations):
            print(f"  Iteration {iteration + 1}/{max_iterations}")

            # Store previous transforms for convergence check
            prev_transforms = [H.copy() for H in self.global_transforms]

            # Adjust transforms to minimize global inconsistency
            total_adjustment = 0.0

            for i in range(1, len(self.global_transforms)):
                if i-1 < len(self.pairwise_transforms) and self.pairwise_transforms[i-1] is not None:
                    # Expected transform based on previous global transform and pairwise
                    expected_H = np.dot(self.global_transforms[i-1], self.pairwise_transforms[i-1])

                    # Compute adjustment (weighted average)
                    weight = 0.1  # Small adjustment factor
                    adjustment = expected_H - self.global_transforms[i]
                    self.global_transforms[i] += weight * adjustment

                    total_adjustment += np.linalg.norm(adjustment)

            print(f"    Total adjustment: {total_adjustment:.6f}")

            # Check convergence
            if total_adjustment < convergence_threshold:
                print(f"    ✅ Converged after {iteration + 1} iterations")
                break

        print("✅ Global refinement completed")

    def find_canvas_size(self):
        """Calculate output canvas size based on global transforms"""
        print("\n🖼️  Calculating canvas size...")

        all_corners = []

        for i, (img, H) in enumerate(zip(self.images, self.global_transforms)):
            h, w = img.shape[:2]
            corners = np.array([[0, 0, 1], [w, 0, 1], [w, h, 1], [0, h, 1]]).T

            # Transform corners
            transformed_corners = np.dot(H, corners)
            transformed_corners = transformed_corners[:2] / transformed_corners[2]

            all_corners.extend(transformed_corners.T)

        all_corners = np.array(all_corners)

        # Find bounding box
        min_x, min_y = np.min(all_corners, axis=0)
        max_x, max_y = np.max(all_corners, axis=0)

        # Add padding
        padding = 50
        min_x -= padding
        min_y -= padding
        max_x += padding
        max_y += padding

        canvas_width = int(max_x - min_x)
        canvas_height = int(max_y - min_y)

        # Translation to make all coordinates positive
        self.translation = np.array([[-min_x], [-min_y], [0]])

        print(f"Canvas size: {canvas_width} x {canvas_height}")
        print(f"Translation: ({-min_x:.1f}, {-min_y:.1f})")

        return canvas_width, canvas_height




    def create_seamless_mosaic(self, folder_path):
        """Step 5: Create Seamless Mosaic with Advanced Blending"""
        print("\n" + "="*50)
        print("🎨 STEP 5: Creating Seamless Mosaic")
        print("="*50)

        canvas_width, canvas_height = self.find_canvas_size()

        # Compute exposure compensation
        self._compute_exposure_compensation()

        # Create canvas
        if self.images[0].ndim == 3:
            canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float64)
            weight_sum = np.zeros((canvas_height, canvas_width), dtype=np.float64)
        else:
            canvas = np.zeros((canvas_height, canvas_width), dtype=np.float64)
            weight_sum = np.zeros((canvas_height, canvas_width), dtype=np.float64)

        print("🔄 Applying transforms and blending...")

        # Process each image
        for i, (img, H) in enumerate(zip(self.images, self.global_transforms)):
            # Apply exposure compensation
            if hasattr(self, 'exposure_gains'):
                img_corrected = img.astype(np.float64) * self.exposure_gains[i]
                img_corrected = np.clip(img_corrected, 0, 255).astype(np.uint8)
            else:
                img_corrected = img

            # Add translation to transform
            H_translated = H.copy()
            H_translated[:2, 2] += self.translation[:2, 0]

            # Warp image
            if img.ndim == 3:
                warped = cv2.warpPerspective(img_corrected, H_translated, (canvas_width, canvas_height))
            else:
                warped = cv2.warpPerspective(img_corrected, H_translated, (canvas_width, canvas_height))

            # Create advanced blending mask
            mask = self._create_blending_mask(img, H_translated, canvas_width, canvas_height)

            # Blend with weights
            if img.ndim == 3:
                for c in range(3):
                    canvas[:,:,c] += warped[:,:,c].astype(np.float64) * mask
            else:
                canvas += warped.astype(np.float64) * mask

            weight_sum += mask

            print(f"  Processed image {i+1}/{len(self.images)}")

        # Normalize
        weight_sum[weight_sum == 0] = 1
        if self.images[0].ndim == 3:
            for c in range(3):
                canvas[:,:,c] /= weight_sum
        else:
            canvas /= weight_sum

        # Convert back to uint8
        canvas = np.clip(canvas, 0, 255).astype(np.uint8)

        # Save result
        output_path = os.path.join(folder_path, "enhanced_stitching_result.tif")
        cv2.imwrite(output_path, canvas)
        print(f"✅ Seamless mosaic saved: {output_path}")

        return canvas, output_path

    def _compute_exposure_compensation(self):
        """Compute exposure compensation gains"""
        print("💡 Computing exposure compensation...")

        exposures = []
        for img in self.images:
            if img.ndim == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img

            mean_intensity = np.mean(gray)
            exposures.append(mean_intensity)

        # Normalize to reference (first image)
        reference_exposure = exposures[0]
        self.exposure_gains = []

        for i, exposure in enumerate(exposures):
            gain = reference_exposure / exposure if exposure > 0 else 1.0
            self.exposure_gains.append(gain)
            print(f"  Image {i}: Exposure gain = {gain:.3f}")

        print("✅ Exposure compensation computed")

    def _create_blending_mask(self, img, H_translated, canvas_width, canvas_height):
        """Create advanced blending mask with distance-based feathering"""
        # Create initial mask
        mask = np.zeros((img.shape[0], img.shape[1]), dtype=np.float64)

        # Create mask based on non-zero pixels
        if img.ndim == 3:
            mask_condition = np.any(img > 0, axis=2)
        else:
            mask_condition = img > 0

        mask[mask_condition] = 1.0

        # Apply distance transform for smooth feathering
        from scipy.ndimage import distance_transform_edt

        # Create distance from edges
        distance_from_edge = distance_transform_edt(mask)

        # Normalize distance for feathering (max distance = 1.0)
        max_distance = np.max(distance_from_edge)
        if max_distance > 0:
            distance_from_edge = distance_from_edge / max_distance

        # Apply feathering function (smooth transition)
        feather_width = 30  # pixels
        feathered_mask = np.minimum(distance_from_edge / feather_width, 1.0)

        # Warp the mask
        mask_warped = cv2.warpPerspective(feathered_mask, H_translated, (canvas_width, canvas_height))

        return mask_warped

    def print_summary(self):
        """Print detailed summary of the stitching process"""
        print("\n" + "="*60)
        print("📊 ENHANCED IMAGE STITCHING SUMMARY")
        print("="*60)

        print(f"📁 Total images processed: {len(self.images)}")
        print(f"🔗 Pairwise transforms computed: {len(self.pairwise_transforms)}")

        # Method breakdown
        if self.transform_methods:
            feature_count = sum(1 for m in self.transform_methods if "Feature-based" in m)
            phase_count = sum(1 for m in self.transform_methods if "Phase Correlation" in m)
            failed_count = sum(1 for m in self.transform_methods if "Failed" in m)

            print(f"\n🎯 Transform Methods Used:")
            print(f"  ✅ Feature-based (ORB): {feature_count}")
            print(f"  📐 Phase Correlation: {phase_count}")
            print(f"  ❌ Failed: {failed_count}")

            # Print details for each pair
            print(f"\n📋 Detailed Method Report:")
            for i, method in enumerate(self.transform_methods):
                img1_name = os.path.basename(self.image_paths[i]) if i < len(self.image_paths) else f"Image {i}"
                img2_name = os.path.basename(self.image_paths[i+1]) if i+1 < len(self.image_paths) else f"Image {i+1}"
                print(f"  {img1_name} → {img2_name}: {method}")

        print(f"\n🌐 Global optimization: ✅ Completed")
        print(f"🎨 Advanced blending: ✅ Completed")
        print(f"💡 Exposure compensation: ✅ Applied")

def main():
    """Main function to run the enhanced image stitching pipeline"""
    # Default folder path - can be overridden by command line argument
    folder_path = "."  # Current directory

    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]

    if not os.path.exists(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        print("Usage: python proper_image_stitching.py [folder_path]")
        return

    print("🔬 ENHANCED IMAGE STITCHING PIPELINE")
    print("=" * 60)
    print("🎯 Pipeline Image Stitching dengan Global Optimization")
    print("📁 Input folder:", folder_path)

    # Initialize enhanced stitcher
    stitcher = EnhancedImageStitcher()

    try:
        start_time = time.time()

        # Step 1: Load and preprocess images
        num_images = stitcher.load_images(folder_path)
        if num_images < 2:
            print("❌ Need at least 2 images for stitching")
            return

        # Step 2: Detect features
        stitcher.detect_features()

        # Step 3: Compute pairwise transformations (with fallback methods)
        successful_transforms = stitcher.compute_pairwise_transforms()
        if successful_transforms == 0:
            print("❌ No successful pairwise transformations found")
            return

        # Step 4: Global bundle adjustment
        stitcher.global_bundle_adjustment()

        # Step 5: Create seamless mosaic
        result_canvas, output_path = stitcher.create_seamless_mosaic(folder_path)

        # Print detailed summary
        stitcher.print_summary()

        end_time = time.time()
        processing_time = end_time - start_time

        print(f"\n⏱️  Total processing time: {processing_time:.2f} seconds")
        print(f"📄 Final mosaic saved: {output_path}")

        print("\n" + "="*60)
        print("🎉 ENHANCED IMAGE STITCHING COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("✅ Robust pairwise transformation (ORB + Phase Correlation)")
        print("✅ Global bundle adjustment for error minimization")
        print("✅ Advanced blending with distance-based feathering")
        print("✅ Automatic exposure compensation")
        print("✅ Seamless high-resolution mosaic output")
        print("✅ Handles featureless regions automatically")

    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
