#!/usr/bin/env python3
"""
Enhanced Fase 1 with Advanced Seam Finding
Integrates graph-cut seam finding to eliminate grid artifacts
"""

import os
import glob
import cv2
import numpy as np
import tifffile
from PIL import Image
from seam_finding import SeamFinder, create_overlap_regions

# --- FASE 1 & 2 (Disederhanakan): Hitung Posisi Grid dan <PERSON> ---
def get_image_positions(folder_path, rows, cols, overlap_pct):
    print("Mencari posisi gambar...")
    
    image_files = sorted(glob.glob(os.path.join(folder_path, "*.png")) + glob.glob(os.path.join(folder_path, "*.jpg")))
    if not image_files: return None, None
    
    first_img = cv2.imread(image_files[0], cv2.IMREAD_GRAYSCALE)
    if first_img is None: return None, None
    h, w = first_img.shape[:2]

    positions = {}
    for i in range(len(image_files)):
        current_row = i // cols
        current_col = i % cols
        if current_row % 2 == 1:
            current_col = cols - 1 - current_col
        display_row = rows - 1 - current_row

        # Hitung posisi grid dasar
        pos_x_base = current_col * w * (1 - overlap_pct)
        pos_y_base = display_row * h * (1 - overlap_pct)
        
        # Simpan posisi dasar
        positions[i] = [pos_x_base, pos_y_base]

    # Tambahkan koreksi dari Phase Correlation
    print("Menambahkan koreksi halus dari Phase Correlation...")
    for i in range(len(image_files)):
        current_row = i // cols
        current_col = i % cols
        if current_row % 2 == 1: current_col = cols - 1 - current_col

        # Koreksi Horizontal
        if current_col < cols - 1:
            neighbor_idx = current_row * cols + (current_col + 1)
            if neighbor_idx < len(image_files):
                dx, dy = compute_phase_correlation(image_files[i], image_files[neighbor_idx])
                positions[neighbor_idx][0] += dx
                positions[neighbor_idx][1] += dy

        # Koreksi Vertikal
        if current_row < rows - 1:
            neighbor_idx = (current_row + 1) * cols + current_col
            if neighbor_idx < len(image_files):
                dx, dy = compute_phase_correlation(image_files[i], image_files[neighbor_idx])
                positions[neighbor_idx][0] += dx
                positions[neighbor_idx][1] += dy
            
    return image_files, positions

def compute_phase_correlation(img1_path, img2_path):
    img1 = cv2.imread(img1_path, cv2.IMREAD_GRAYSCALE)
    img2 = cv2.imread(img2_path, cv2.IMREAD_GRAYSCALE)
    if img1 is None or img2 is None: return 0, 0
    img1_float = np.float32(img1)
    img2_float = np.float32(img2)
    rows, cols = img1_float.shape
    img2_float = cv2.resize(img2_float, (cols, rows))
    output, _ = cv2.phaseCorrelate(img1_float, img2_float)
    return output

# --- FASE 3: Penyambungan dengan Seam Finding ---
def create_seamless_mosaic_with_seams(image_files, positions, rows, cols, overlap_pct=0.2):
    print("Memulai Fase 3: Penyambungan dengan Seam Finding...")
    
    # Load first image to get dimensions
    first_img = cv2.imread(image_files[0])
    h, w = first_img.shape[:2]
    is_color = len(first_img.shape) == 3

    # Calculate mosaic dimensions
    min_x = min(pos[0] for pos in positions.values())
    max_x = max(pos[0] + w for pos in positions.values())
    min_y = min(pos[1] for pos in positions.values())
    max_y = max(pos[1] + h for pos in positions.values())

    mosaic_w = int(max_x - min_x)
    mosaic_h = int(max_y - min_y)
    
    print(f"Mosaic dimensions: {mosaic_w} x {mosaic_h}")
    
    # Initialize seam finder
    blend_width = int(min(h, w) * overlap_pct / 2)
    seam_finder = SeamFinder(blend_width=blend_width)
    
    # Create position list for overlap calculation
    position_list = [(positions[i][1] - min_y, positions[i][0] - min_x) for i in range(len(image_files))]
    
    # Find overlap regions
    print("Mencari region overlap...")
    overlap_regions = create_overlap_regions(position_list, (h, w), overlap_pct)
    print(f"Ditemukan {len(overlap_regions)} region overlap")
    
    # Load all images
    print("Loading semua gambar...")
    images = []
    for img_path in image_files:
        img = cv2.imread(img_path)
        # Convert BGR to RGB for correct color display
        if len(img.shape) == 3:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        images.append(img)
    
    # Calculate seams for all overlaps
    print("Menghitung seam optimal...")
    seam_masks = {}
    
    for overlap in overlap_regions:
        img1_idx = overlap['img1_idx']
        img2_idx = overlap['img2_idx']
        region = overlap['region']
        direction = overlap['direction']
        
        print(f"  Processing overlap: Image {img1_idx} <-> Image {img2_idx} ({direction})")
        
        # Get images
        img1 = images[img1_idx]
        img2 = images[img2_idx]
        
        # Calculate overlap region in image coordinates
        pos1 = position_list[img1_idx]
        pos2 = position_list[img2_idx]
        
        # Convert global overlap region to local image coordinates
        y1, y2, x1, x2 = region

        # Local coordinates for img1
        local_y1_1 = max(0, int(y1 - pos1[0]))
        local_y2_1 = min(h, int(y2 - pos1[0]))
        local_x1_1 = max(0, int(x1 - pos1[1]))
        local_x2_1 = min(w, int(x2 - pos1[1]))

        # Local coordinates for img2
        local_y1_2 = max(0, int(y1 - pos2[0]))
        local_y2_2 = min(h, int(y2 - pos2[0]))
        local_x1_2 = max(0, int(x1 - pos2[1]))
        local_x2_2 = min(w, int(x2 - pos2[1]))
        
        # Extract overlap regions
        if (local_y2_1 > local_y1_1 and local_x2_1 > local_x1_1 and 
            local_y2_2 > local_y1_2 and local_x2_2 > local_x1_2):
            
            overlap_region = (local_y1_1, local_y2_1, local_x1_1, local_x2_1)
            
            try:
                seam_mask = seam_finder.find_optimal_seam(
                    img1, img2, overlap_region, direction
                )
                seam_masks[(img1_idx, img2_idx)] = {
                    'mask': seam_mask,
                    'region': region,
                    'local_region_1': overlap_region,
                    'local_region_2': (local_y1_2, local_y2_2, local_x1_2, local_x2_2)
                }
            except Exception as e:
                print(f"    Warning: Seam finding failed for {img1_idx}-{img2_idx}: {e}")
    
    # Create final mosaic with seam-based blending
    print("Membuat mosaic final dengan seam blending...")
    
    if is_color:
        final_mosaic = np.zeros((mosaic_h, mosaic_w, 3), dtype=np.float32)
        weight_sum = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
    else:
        final_mosaic = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
        weight_sum = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
    
    # Place images with seam-aware blending
    for i in range(len(image_files)):
        img = images[i].astype(np.float32)
        
        pos_x = int(positions[i][0] - min_x)
        pos_y = int(positions[i][1] - min_y)
        
        # Create base weight map (distance-based feathering)
        tile_weight = create_distance_weight(img.shape[:2], blend_width)
        
        # Modify weight based on seams
        tile_weight = apply_seam_weights(tile_weight, i, seam_masks, position_list)
        
        # Place image
        h_part, w_part = img.shape[:2]
        
        if is_color:
            for c in range(3):
                final_mosaic[pos_y:pos_y+h_part, pos_x:pos_x+w_part, c] += img[:,:,c] * tile_weight
        else:
            final_mosaic[pos_y:pos_y+h_part, pos_x:pos_x+w_part] += img * tile_weight
        
        weight_sum[pos_y:pos_y+h_part, pos_x:pos_x+w_part] += tile_weight
        
        if i % 5 == 0:
            print(f"  Placed image {i+1}/{len(image_files)}")

    # Normalize
    weight_sum[weight_sum == 0] = 1.0
    if is_color:
        for c in range(3):
            final_mosaic[:,:,c] /= weight_sum
    else:
        final_mosaic /= weight_sum
    
    final_mosaic = final_mosaic.astype(np.uint8)
    
    # Save result
    output_path = os.path.join(os.path.dirname(image_files[0]), "seamless_mosaic_with_seams.tif")
    tifffile.imwrite(output_path, final_mosaic)

    print(f"\nPenyambungan Selesai. Mozaik tersimpan di: {output_path}")
    return final_mosaic, output_path

def create_distance_weight(shape, blend_width):
    """Create distance-based weight map"""
    h, w = shape
    
    # Create binary mask
    mask = np.ones((h, w), dtype=np.float32)
    mask[0, :] = 0  # top edge
    mask[-1, :] = 0  # bottom edge
    mask[:, 0] = 0  # left edge
    mask[:, -1] = 0  # right edge
    
    # Distance transform
    from scipy.ndimage import distance_transform_edt
    distance = distance_transform_edt(mask)
    
    # Create smooth weight
    weight = np.minimum(distance / blend_width, 1.0)
    weight = 0.5 * (1 + np.tanh(4 * (weight - 0.5)))
    
    return weight

def apply_seam_weights(base_weight, img_idx, seam_masks, positions):
    """Apply seam-based weight modifications"""
    modified_weight = base_weight.copy()

    # Apply seam masks where this image overlaps with others
    for (idx1, idx2), seam_data in seam_masks.items():
        if img_idx == idx1:
            # This image is img1 in the seam calculation
            mask = seam_data['mask']
            region = seam_data['local_region_1']
            y1, y2, x1, x2 = region

            if y2 > y1 and x2 > x1:
                # Ensure mask dimensions match the region
                region_h, region_w = y2 - y1, x2 - x1
                if mask.shape != (region_h, region_w):
                    mask = cv2.resize(mask, (region_w, region_h))
                modified_weight[y1:y2, x1:x2] *= mask

        elif img_idx == idx2:
            # This image is img2 in the seam calculation
            mask = 1.0 - seam_data['mask']  # Invert mask for img2
            region = seam_data['local_region_2']
            y1, y2, x1, x2 = region

            if y2 > y1 and x2 > x1:
                # Ensure mask dimensions match the region
                region_h, region_w = y2 - y1, x2 - x1
                if mask.shape != (region_h, region_w):
                    mask = cv2.resize(mask, (region_w, region_h))
                modified_weight[y1:y2, x1:x2] *= mask

    return modified_weight

# --- Alur Utama ---
if __name__ == "__main__":
    folder_path = r"D:\Stitch\bilinear_stitching_20250915_152529"
    rows = 4
    cols = 9
    overlap_pct = 0.2
    
    print("="*60)
    print("🔬 ENHANCED MICROSCOPIC STITCHING WITH SEAM FINDING")
    print("="*60)
    
    files, positions = get_image_positions(folder_path, rows, cols, overlap_pct)
    if files and positions:
        mosaic, output_path = create_seamless_mosaic_with_seams(files, positions, rows, cols, overlap_pct)
        print(f"\n✅ Seam-based stitching completed!")
        print(f"📁 Output: {output_path}")
    else:
        print("❌ Error: Could not load images or calculate positions")
