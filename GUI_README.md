# 🔬 Microscopic Image Stitching GUI

GUI interface untuk stitching gambar mikroskopis dengan fitur validasi koneksi tile dan visualisasi graph matches.

## ✨ Fitur Utama

### 📁 **File Selection & Validation**
- Pemilihan folder dengan tile images
- Validasi format nama file: `tile_{r}_{c}.png`
- Deteksi otomatis grid layout (rows x columns)
- Konfirmasi jumlah file yang ditemukan

### 🔗 **Tile Connection Analysis**
- Generasi matches graph antar tile yang bersebelahan
- Visualisasi informasi koneksi dalam format:
  ```
  "tile_01_00.png" -- "tile_01_01.png"[label="Nm=33, Ni=23, C=1.28492"];
  ```
- Crosscheck koneksi tile - hanya tile yang bersebelahan yang terhubung
- Validasi integritas grid (tidak ada tile yang terisolasi)

### ⚙️ **Configurable Stitching**
- **Seam Finding**: Eliminasi grid artifacts dengan graph-cut optimization
- **Phase Correlation**: Untuk gambar dengan sedikit feature
- **Feature-based**: Untuk gambar dengan banyak texture/feature
- Parameter yang dapat disesuaikan:
  - Overlap threshold (0.1-0.8)
  - Minimum matches (5-100)
  - Stitching method selection

### 🎯 **Smart Processing**
- Hanya memproses tile yang terhubung dengan benar
- Deteksi multiple components (menghasilkan beberapa gambar output)
- Progress monitoring dengan threading
- Error handling yang robust

## 🚀 Cara Penggunaan

### 1. **Instalasi Dependencies**
```bash
pip install numpy opencv-python networkx tkinter
```

### 2. **Menjalankan GUI**
```bash
# Jalankan GUI
python run_stitching_gui.py

# Buat sample data untuk testing
python run_stitching_gui.py --create-sample 3 4  # 3 rows, 4 columns

# Lihat help
python run_stitching_gui.py --help
```

### 3. **Workflow dalam GUI**

#### **Step 1: Select Files**
1. Klik **"Select Folder"**
2. Pilih folder yang berisi tile images
3. Klik **"Analyze Files"**
4. Periksa informasi di panel "File Information"

#### **Step 2: Generate Matches Graph**
1. Klik **"Generate Matches Graph"**
2. Tunggu proses analisis selesai
3. Periksa hasil di panel "Tile Connections"
4. Format output:
   ```
   "tile_00_01.png" -- "tile_00_02.png"[label="Nm=45, Ni=32, C=0.71111"];
   "tile_00_02.png" -- "tile_01_02.png"[label="Nm=38, Ni=27, C=0.71053"];
   ```

#### **Step 3: Validate Connections**
1. Klik **"Validate Connections"**
2. Periksa hasil validasi:
   - ✅ All tiles connected in single component
   - ⚠️ Multiple separate components found
   - ❌ Isolated tiles detected

#### **Step 4: Configure & Stitch**
1. Pilih stitching method (recommended: **seam_finding**)
2. Adjust parameters jika diperlukan
3. Klik **"Start Stitching"**
4. Tunggu proses selesai

## 📊 Informasi Matches Graph

### **Format Output**
```
"tile_01_00.png" -- "tile_01_01.png"[label="Nm=33, Ni=23, C=1.28492"];
```

**Keterangan:**
- **Nm**: Total number of matches found
- **Ni**: Number of good/inlier matches  
- **C**: Match quality/confidence score

### **Validasi Koneksi**
- **Adjacent Only**: Hanya tile yang bersebelahan (horizontal/vertical) yang dianalisis
- **No Diagonal**: Tidak ada koneksi diagonal untuk menjaga struktur grid
- **Proper Grid**: Memastikan setiap tile terhubung dengan tetangganya yang benar

## ⚙️ Parameter Stitching

### **Overlap Threshold (0.1-0.8)**
- **0.1-0.2**: Overlap minimal, processing cepat
- **0.3-0.4**: Overlap standar, hasil seimbang  
- **0.5-0.8**: Overlap tinggi, hasil lebih akurat

### **Min Matches (5-100)**
- **5-15**: Threshold rendah, lebih banyak koneksi
- **20-50**: Threshold standar, koneksi berkualitas
- **50-100**: Threshold tinggi, hanya koneksi terbaik

### **Stitching Methods**

#### **🎯 Seam Finding (Recommended)**
- **Best for**: Eliminasi grid artifacts
- **Features**: Graph-cut optimization, multi-criteria cost function
- **Output**: Seamless mosaic tanpa garis grid
- **Speed**: Medium-slow, hasil terbaik

#### **⚡ Phase Correlation**
- **Best for**: Gambar dengan sedikit feature
- **Features**: Frequency domain alignment
- **Output**: Cepat tapi mungkin ada blur
- **Speed**: Fast

#### **🔍 Feature-based**
- **Best for**: Gambar dengan banyak texture/feature
- **Features**: SIFT/ORB feature matching
- **Output**: Akurat untuk high-feature images
- **Speed**: Medium

## 📁 Format File Input

### **Naming Convention**
```
tile_{row}_{column}.png
```

**Contoh:**
```
tile_00_00.png  # Row 0, Column 0
tile_00_01.png  # Row 0, Column 1  
tile_01_00.png  # Row 1, Column 0
tile_01_01.png  # Row 1, Column 1
```

### **Supported Formats**
- PNG (recommended)
- JPG/JPEG
- Case insensitive

## 🎯 Output

### **File Output**
- **Seam Finding**: `seamless_mosaic_blend{width}_grad{weight}.tif`
- **Location**: Same folder as input images
- **Format**: High-quality TIFF

### **Multiple Components**
- Jika ada tile groups yang terpisah
- Menghasilkan multiple output images
- User confirmation sebelum processing

## 🔧 Troubleshooting

### **No Files Found**
- Periksa format nama file: `tile_XX_YY.png`
- Pastikan file ada di folder yang dipilih
- Cek case sensitivity

### **No Matches Found**
- Overlap antar tile terlalu kecil
- Kualitas gambar buruk
- Kurangi "Min Matches" threshold
- Coba method yang berbeda

### **Isolated Tiles**
- Ada tile yang tidak terhubung dengan tetangga
- Periksa kualitas gambar tile tersebut
- Mungkin ada gap dalam grid layout

### **Multiple Components**
- Grid tidak continuous
- Ada missing tiles di tengah
- Akan menghasilkan beberapa output terpisah

## 💡 Tips untuk Hasil Terbaik

1. **Pastikan Overlap Cukup**: Minimal 15-20% overlap antar tile
2. **Kualitas Gambar Konsisten**: Exposure dan focus yang seragam
3. **Grid Layout Lengkap**: Hindari missing tiles di tengah grid
4. **Gunakan Seam Finding**: Untuk hasil tanpa grid artifacts
5. **Validasi Dulu**: Selalu check connections sebelum stitching

## 🔗 Integration dengan Existing Code

GUI ini terintegrasi dengan:
- `seam_finding.py`: Advanced seam finding algorithm
- `seam_config.py`: Configurable parameters
- `run_configurable_seam_stitching.py`: Backend stitching engine

Anda dapat extend GUI ini dengan menambahkan method stitching lain atau parameter tambahan sesuai kebutuhan.
