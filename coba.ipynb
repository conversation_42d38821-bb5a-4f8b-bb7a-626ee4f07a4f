from matplotlib import pyplot as plt
import cv2 as cv
import numpy as np

def plot_image(img, figsize_in_inches=(5,5)):
    fig, ax = plt.subplots(figsize=figsize_in_inches)
    ax.imshow(cv.cvtColor(img, cv.COLOR_BGR2RGB))
    plt.show()
    
def plot_images(imgs, figsize_in_inches=(5,5)):
    fig, axs = plt.subplots(1, len(imgs), figsize=figsize_in_inches)
    for col, img in enumerate(imgs):
        axs[col].imshow(cv.cvtColor(img, cv.COLOR_BGR2RGB))
    plt.show()

from pathlib import Path

def get_image_paths(img_set):
    folder = Path.cwd()  # folder di mana kernel dijalankan
    return [str(path.relative_to(folder)) for path in folder.rglob(f'{img_set}*')]

tile_imgs = get_image_paths('tile')
print(tile_imgs)


from stitching.images import Images

images = Images.of(tile_imgs)

medium_imgs = list(images.resize(Images.Resolution.MEDIUM))
low_imgs = list(images.resize(Images.Resolution.LOW))
final_imgs = list(images.resize(Images.Resolution.FINAL))

plot_images(low_imgs, (20,20))

original_size = images.sizes[0]
medium_size = images.get_image_size(medium_imgs[0])
low_size = images.get_image_size(low_imgs[0])
final_size = images.get_image_size(final_imgs[0])

print(f"Original Size: {original_size}  -> {'{:,}'.format(np.prod(original_size))} px ~ 1 MP")
print(f"Medium Size:   {medium_size}  -> {'{:,}'.format(np.prod(medium_size))} px ~ 0.6 MP")
print(f"Low Size:      {low_size}   -> {'{:,}'.format(np.prod(low_size))} px ~ 0.1 MP")
print(f"Final Size:    {final_size}  -> {'{:,}'.format(np.prod(final_size))} px ~ 1 MP")

from stitching.feature_detector import FeatureDetector

finder = FeatureDetector()
features = [finder.detect_features(img) for img in medium_imgs]
keypoints_center_img = finder.draw_keypoints(medium_imgs[1], features[1])

plot_image(keypoints_center_img, (15,10))

from stitching.feature_matcher import FeatureMatcher

matcher = FeatureMatcher()
matches = matcher.match_features(features)

matcher.get_confidence_matrix(matches)

all_relevant_matches = matcher.draw_matches_matrix(medium_imgs, features, matches, conf_thresh=1, 
                                                   inliers=True, matchColor=(0, 255, 0))

for idx1, idx2, img in all_relevant_matches:
    print(f"Matches Image {idx1+1} to Image {idx2+1}")
    plot_image(img, (20,10))

from stitching.subsetter import Subsetter

subsetter = Subsetter()
dot_notation = subsetter.get_matches_graph(images.names, matches)
print(dot_notation)

indices = subsetter.get_indices_to_keep(features, matches)

medium_imgs = subsetter.subset_list(medium_imgs, indices)
low_imgs = subsetter.subset_list(low_imgs, indices)
final_imgs = subsetter.subset_list(final_imgs, indices)
features = subsetter.subset_list(features, indices)
matches = subsetter.subset_matches(matches, indices)

images.subset(indices)

print(images.names)
print(matcher.get_confidence_matrix(matches))

from stitching.camera_estimator import CameraEstimator
from stitching.camera_adjuster import CameraAdjuster
from stitching.camera_wave_corrector import WaveCorrector

CameraEstimator(estimator='homography')
CameraAdjuster(adjuster='ray', refinement_mask='x---x')
wave_corrector = WaveCorrector(wave_correct_kind='horiz')

camera_estimator = CameraEstimator()
camera_adjuster = CameraAdjuster()
wave_corrector = WaveCorrector()

cameras = camera_estimator.estimate(features, matches)
cameras = camera_adjuster.adjust(features, matches, cameras)
cameras = wave_corrector.correct(cameras)

from stitching.warper import Warper

warper = Warper()

warper.set_scale(cameras)

low_sizes = images.get_scaled_img_sizes(Images.Resolution.LOW)
camera_aspect = images.get_ratio(Images.Resolution.MEDIUM, Images.Resolution.LOW)  # since cameras were obtained on medium imgs

warped_low_imgs = list(warper.warp_images(low_imgs, cameras, camera_aspect))
warped_low_masks = list(warper.create_and_warp_masks(low_sizes, cameras, camera_aspect))
low_corners, low_sizes = warper.warp_rois(low_sizes, cameras, camera_aspect)

final_sizes = images.get_scaled_img_sizes(Images.Resolution.FINAL)
camera_aspect = images.get_ratio(Images.Resolution.MEDIUM, Images.Resolution.FINAL)

warped_final_imgs = list(warper.warp_images(final_imgs, cameras, camera_aspect))
warped_final_masks = list(warper.create_and_warp_masks(final_sizes, cameras, camera_aspect))
final_corners, final_sizes = warper.warp_rois(final_sizes, cameras, camera_aspect)

plot_images(warped_low_imgs, (10,10))
plot_images(warped_low_masks, (10,10))

print(final_corners)
print(final_sizes)

from stitching.timelapser import Timelapser

timelapser = Timelapser('as_is')
timelapser.initialize(final_corners, final_sizes)

for img, corner in zip(warped_final_imgs, final_corners):
    timelapser.process_frame(img, corner)
    frame = timelapser.get_frame()
    plot_image(frame, (10,10))

from stitching.cropper import Cropper

cropper = Cropper()

mask = cropper.estimate_panorama_mask(warped_low_imgs, warped_low_masks, low_corners, low_sizes)
plot_image(mask, (5,5))

lir = cropper.estimate_largest_interior_rectangle(mask)


lir = cropper.estimate_largest_interior_rectangle(mask)
print(lir)

plot = lir.draw_on(mask, size=2)
plot_image(plot, (5,5))

low_corners = cropper.get_zero_center_corners(low_corners)
rectangles = cropper.get_rectangles(low_corners, low_sizes)

plot = rectangles[1].draw_on(plot, (0, 255, 0), 2)  # The rectangle of the center img
plot_image(plot, (5,5))

overlap = cropper.get_overlap(rectangles[1], lir)
plot = overlap.draw_on(plot, (255, 0, 0), 2)
plot_image(plot, (5,5))

intersection = cropper.get_intersection(rectangles[1], overlap)
plot = intersection.draw_on(warped_low_masks[1], (255, 0, 0), 2)
plot_image(plot, (2.5,2.5))

cropper.prepare(warped_low_imgs, warped_low_masks, low_corners, low_sizes)

cropped_low_masks = list(cropper.crop_images(warped_low_masks))
cropped_low_imgs = list(cropper.crop_images(warped_low_imgs))
low_corners, low_sizes = cropper.crop_rois(low_corners, low_sizes)

lir_aspect = images.get_ratio(Images.Resolution.LOW, Images.Resolution.FINAL)  # since lir was obtained on low imgs
cropped_final_masks = list(cropper.crop_images(warped_final_masks, lir_aspect))
cropped_final_imgs = list(cropper.crop_images(warped_final_imgs, lir_aspect))
final_corners, final_sizes = cropper.crop_rois(final_corners, final_sizes, lir_aspect)

timelapser = Timelapser('as_is')
timelapser.initialize(final_corners, final_sizes)

for img, corner in zip(cropped_final_imgs, final_corners):
    timelapser.process_frame(img, corner)
    frame = timelapser.get_frame()
    plot_image(frame, (10,10))

from stitching.seam_finder import SeamFinder

seam_finder = SeamFinder()

seam_masks = seam_finder.find(cropped_low_imgs, low_corners, cropped_low_masks)
seam_masks = [seam_finder.resize(seam_mask, mask) for seam_mask, mask in zip(seam_masks, cropped_final_masks)]

seam_masks_plots = [SeamFinder.draw_seam_mask(img, seam_mask) for img, seam_mask in zip(cropped_final_imgs, seam_masks)]
plot_images(seam_masks_plots, (15,10))

from stitching.exposure_error_compensator import ExposureErrorCompensator

compensator = ExposureErrorCompensator()

compensator.feed(low_corners, cropped_low_imgs, cropped_low_masks)

compensated_imgs = [compensator.apply(idx, corner, img, mask) 
                    for idx, (img, mask, corner) 
                    in enumerate(zip(cropped_final_imgs, cropped_final_masks, final_corners))]

from stitching.blender import Blender

blender = Blender()
blender.prepare(final_corners, final_sizes)
for img, mask, corner in zip(compensated_imgs, seam_masks, final_corners):
    blender.feed(img, mask, corner)
panorama, _ = blender.blend()

plot_image(panorama, (20,20))
import cv2

cv2.imwrite("row_0_panorama.png", panorama)

blended_seam_masks = seam_finder.blend_seam_masks(seam_masks, final_corners, final_sizes)
plot_image(blended_seam_masks, (5,5))

plot_image(seam_finder.draw_seam_lines(panorama, blended_seam_masks, linesize=3), (15,10))
plot_image(seam_finder.draw_seam_polygons(panorama, blended_seam_masks), (15,10))

from stitching import Stitcher

stitcher = Stitcher()
panorama = stitcher.stitch(tile_imgs)  # the user is warned that only a subset of input images is stitched

plot_image(panorama, (20,20))

from pathlib import Path
import re
from collections import defaultdict

def group_tiles_by_row_overlap(folder, max_cols=9, overlap=3):
    folder = Path(folder)
    pattern = re.compile(r"tile_(\d{3})_(\d{3})\.png")
    rows = defaultdict(list)

    # kumpulkan tile per row
    for path in folder.glob("tile_*.png"):
        m = pattern.match(path.name)
        if not m:
            continue
        row, col = int(m.group(1)), int(m.group(2))
        rows[row].append((col, str(path)))

    # sort tiap row by col
    for r in rows:
        rows[r].sort(key=lambda x: x[0])

    # pecah jadi beberapa group dengan overlap
    groups = {}
    for r, files in sorted(rows.items()):
        col_count = len(files)
        start = 0
        chunk_idx = 0
        while start < col_count:
            end = min(start + max_cols, col_count)
            chunk_files = [f for _, f in files[start:end]]
            groups[f"tile_{r}_{chunk_idx}"] = chunk_files

            if end == col_count:  # kalau sudah habis, break
                break
            start = end - overlap
            chunk_idx += 1

    return groups

folder = r"D:\Stitch\bilinear_stitching_20250923_101006"
groups = group_tiles_by_row_overlap(folder, max_cols=9, overlap=3)

for group_name in groups:
    print(group_name)



from stitching import Stitcher

stitcher = Stitcher()
panoram = stitcher.stitch(groups["tile_2_0"])  # the user is warned that only a subset of input images is stitched
plot_image(panoram, (20,20))

from stitching import Stitcher

stitcher = Stitcher()
panora = stitcher.stitch(groups["tile_2_1"])  
plot_image(panora, (20,20))

import cv2

cv2.imwrite("tile_2_0_panorama.png", panoram)
cv2.imwrite("tile_2_1_panorama.png", panora)


from stitching import Stitcher

stitcher = Stitcher()
final_pano = stitcher.stitch([
    "tile_2_0_panorama.png",
    "tile_2_1_panorama.png"
])
plot_image(final_pano, (20,20))


cv2.imwrite("tile_2panorama.png", final_pano)

