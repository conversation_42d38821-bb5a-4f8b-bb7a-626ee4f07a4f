#!/usr/bin/env python3
"""
Runner script for Stitching GUI
Includes dependency checking and sample data generation
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    # Check core dependencies
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
        
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
        
    try:
        import networkx
    except ImportError:
        missing_deps.append("networkx")
        
    # Check optional dependencies
    optional_missing = []
    try:
        from stitching.images import Images
    except ImportError:
        optional_missing.append("stitching")
        
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nInstall with: pip install " + " ".join(missing_deps))
        return False
        
    if optional_missing:
        print("⚠️  Missing optional dependencies:")
        for dep in optional_missing:
            print(f"   - {dep}")
        print("Some features will be limited.")
        
    print("✅ Dependencies check passed")
    return True

def create_sample_tiles(output_dir="sample_tiles", grid_size=(3, 3)):
    """Create sample tile images for testing"""
    import numpy as np
    import cv2
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    tile_width, tile_height = 200, 200
    overlap = 40  # 20% overlap
    
    print(f"🎨 Creating {grid_size[0]}x{grid_size[1]} sample tiles in {output_dir}/")
    
    for row in range(grid_size[0]):
        for col in range(grid_size[1]):
            # Create a unique pattern for each tile
            img = np.zeros((tile_height, tile_width, 3), dtype=np.uint8)
            
            # Background gradient
            for y in range(tile_height):
                for x in range(tile_width):
                    img[y, x] = [
                        (row * 80 + x // 4) % 255,
                        (col * 80 + y // 4) % 255,
                        ((row + col) * 60 + (x + y) // 8) % 255
                    ]
            
            # Add some features for matching
            # Grid lines
            cv2.line(img, (0, tile_height//2), (tile_width, tile_height//2), (255, 255, 255), 2)
            cv2.line(img, (tile_width//2, 0), (tile_width//2, tile_height), (255, 255, 255), 2)
            
            # Corner markers
            cv2.circle(img, (20, 20), 10, (255, 0, 0), -1)
            cv2.circle(img, (tile_width-20, 20), 10, (0, 255, 0), -1)
            cv2.circle(img, (20, tile_height-20), 10, (0, 0, 255), -1)
            cv2.circle(img, (tile_width-20, tile_height-20), 10, (255, 255, 0), -1)
            
            # Add text identifier
            text = f"({row},{col})"
            cv2.putText(img, text, (tile_width//2-30, tile_height//2+5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Add some noise for realism
            noise = np.random.randint(-20, 20, img.shape, dtype=np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            # Save tile
            filename = f"tile_{row:02d}_{col:02d}.png"
            filepath = os.path.join(output_dir, filename)
            cv2.imwrite(filepath, img)
            
    print(f"✅ Created {grid_size[0] * grid_size[1]} sample tiles")
    return output_dir

def show_usage_instructions():
    """Show usage instructions"""
    instructions = """
🔬 MICROSCOPIC IMAGE STITCHING GUI

📋 USAGE INSTRUCTIONS:

1. 📁 SELECT FILES:
   - Click "Select Folder" to choose folder with tile images
   - Files must be named: tile_XX_YY.png (XX=row, YY=column)
   - Example: tile_00_01.png, tile_01_02.png, etc.

2. 📊 ANALYZE FILES:
   - Click "Analyze Files" to scan and validate tile files
   - Check file information panel for grid layout

3. 🔗 GENERATE MATCHES:
   - Click "Generate Matches Graph" to find tile connections
   - Review connection information in graph panel
   - Format: "tile_01_00.png" -- "tile_01_01.png"[label="Nm=33, Ni=23, C=1.28492"]

4. 🔍 VALIDATE:
   - Click "Validate Connections" to check grid integrity
   - Ensure all tiles are properly connected

5. 🧩 STITCH:
   - Choose stitching method (seam_finding recommended)
   - Adjust parameters if needed
   - Click "Start Stitching" to begin

⚙️ PARAMETERS:
   - Overlap Threshold: Expected overlap between tiles (0.1-0.8)
   - Min Matches: Minimum feature matches required (5-100)
   - Method: seam_finding (best), phase_correlation, feature_based

📝 NOTES:
   - Seam finding method eliminates grid artifacts
   - Phase correlation works for low-feature images
   - Feature-based requires good texture/features
   - Results saved as TIFF files in input folder
"""
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    messagebox.showinfo("Usage Instructions", instructions)
    root.destroy()

def main():
    """Main function"""
    print("🚀 Starting Microscopic Image Stitching GUI")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot start GUI due to missing dependencies")
        return False
        
    # Check for command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            show_usage_instructions()
            return True
        elif sys.argv[1] == "--create-sample":
            grid_size = (3, 3)
            if len(sys.argv) > 3:
                try:
                    grid_size = (int(sys.argv[2]), int(sys.argv[3]))
                except ValueError:
                    print("Invalid grid size. Using default 3x3")
            
            output_dir = create_sample_tiles(grid_size=grid_size)
            print(f"\n✅ Sample tiles created in: {output_dir}")
            print("You can now use this folder in the GUI for testing")
            return True
    
    # Start GUI
    try:
        from stitching_gui import StitchingGUI
        
        print("🖥️  Starting GUI...")
        root = tk.Tk()
        app = StitchingGUI(root)
        
        # Show instructions on first run
        root.after(1000, show_usage_instructions)
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ Failed to start GUI: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Install missing dependencies")
        print("2. Run with --create-sample to generate test data")
        print("3. Run with --help for usage instructions")
        sys.exit(1)
    else:
        print("\n👋 GUI closed successfully")
        sys.exit(0)
