#!/usr/bin/env python3
"""
Simple runner for seam-based stitching
Tests the implementation with real microscopic images
"""

import os
import sys
import time
from fase_1_with_seam_finding import get_image_positions, create_seamless_mosaic_with_seams

def main():
    """Main function to run seam-based stitching"""
    
    # Configuration - adjust these paths and parameters as needed
    folder_path = r"D:\Stitch\bilinear_stitching_20250915_152529"
    rows = 4
    cols = 9
    overlap_pct = 0.2
    
    print("🔬 SEAM-BASED MICROSCOPIC IMAGE STITCHING")
    print("=" * 60)
    print(f"📁 Input folder: {folder_path}")
    print(f"📐 Grid layout: {rows} x {cols}")
    print(f"🔄 Overlap: {overlap_pct * 100:.1f}%")
    print("=" * 60)
    
    # Check if folder exists
    if not os.path.exists(folder_path):
        print(f"❌ Error: Folder not found: {folder_path}")
        print("Please update the folder_path variable in the script.")
        return False
    
    # Check for images
    import glob
    image_files = glob.glob(os.path.join(folder_path, "*.png")) + glob.glob(os.path.join(folder_path, "*.jpg"))
    if not image_files:
        print(f"❌ Error: No PNG or JPG images found in {folder_path}")
        return False
    
    print(f"✅ Found {len(image_files)} images")
    
    # Start processing
    start_time = time.time()
    
    try:
        # Step 1: Calculate positions
        print("\n🔍 Step 1: Calculating image positions...")
        files, positions = get_image_positions(folder_path, rows, cols, overlap_pct)
        
        if not files or not positions:
            print("❌ Error: Could not calculate image positions")
            return False
        
        print(f"✅ Calculated positions for {len(files)} images")
        
        # Step 2: Create seamless mosaic with seam finding
        print("\n🎨 Step 2: Creating seamless mosaic with seam finding...")
        mosaic, output_path = create_seamless_mosaic_with_seams(
            files, positions, rows, cols, overlap_pct
        )
        
        # Calculate processing time
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Success summary
        print("\n" + "=" * 60)
        print("🎉 SEAM-BASED STITCHING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📄 Output saved: {output_path}")
        print(f"🖼️  Mosaic shape: {mosaic.shape}")
        
        # Additional info
        print("\n📊 Processing Summary:")
        print(f"  • Input images: {len(files)}")
        print(f"  • Grid layout: {rows} x {cols}")
        print(f"  • Overlap percentage: {overlap_pct * 100:.1f}%")
        print(f"  • Output format: TIFF")
        
        print("\n✨ Key Features Applied:")
        print("  ✅ Grid-based positioning with serpentine pattern")
        print("  ✅ Phase correlation for fine alignment")
        print("  ✅ Graph-cut seam finding for optimal transitions")
        print("  ✅ Multi-criteria cost function (gradient + color + texture)")
        print("  ✅ Distance-based feathering for smooth blending")
        print("  ✅ Seam-aware weight distribution")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """Check if all required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_modules = [
        'numpy', 'cv2', 'tifffile', 'PIL', 'scipy'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"  ❌ {module}")
    
    if missing_modules:
        print(f"\n❌ Missing dependencies: {', '.join(missing_modules)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    print("✅ All dependencies available")
    return True

if __name__ == "__main__":
    print("🚀 Starting Seam-Based Image Stitching...")
    
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Run main processing
    success = main()
    
    if success:
        print("\n🎯 Next Steps:")
        print("  1. Open the output TIFF file to examine the results")
        print("  2. Compare with previous stitching methods")
        print("  3. Adjust parameters if needed:")
        print("     - blend_width in SeamFinder for smoother transitions")
        print("     - cost_weights for different seam preferences")
        print("     - overlap_pct for different overlap amounts")
        
        print("\n💡 Tips for Best Results:")
        print("  • Seam finding works best with 15-30% overlap")
        print("  • For very low-feature images, increase blend_width")
        print("  • For high-contrast edges, increase gradient weight")
        print("  • Check seam_test_results/ folder for debug visualizations")
        
        sys.exit(0)
    else:
        print("\n🔧 Troubleshooting:")
        print("  1. Check that the folder path is correct")
        print("  2. Ensure images are in PNG or JPG format")
        print("  3. Verify grid dimensions match your image layout")
        print("  4. Check that overlap percentage is reasonable (0.1-0.4)")
        
        sys.exit(1)
