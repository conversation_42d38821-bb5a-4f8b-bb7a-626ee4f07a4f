#!/usr/bin/env python3
"""
Test Script for Seam Finding Implementation
Tests the seam finding functionality with sample data
"""

import os
import sys
import numpy as np
import cv2
from seam_finding import SeamFinder, create_overlap_regions

def test_seam_finder_basic():
    """Test basic seam finding functionality"""
    print("🧪 Testing Basic Seam Finding...")
    
    # Create synthetic test images
    h, w = 200, 200
    
    # Image 1: Gradient from left to right
    img1 = np.zeros((h, w, 3), dtype=np.uint8)
    for i in range(w):
        img1[:, i] = [i * 255 // w, 100, 150]
    
    # Image 2: Gradient from top to bottom  
    img2 = np.zeros((h, w, 3), dtype=np.uint8)
    for i in range(h):
        img2[i, :] = [150, i * 255 // h, 100]
    
    # Add some noise and features
    np.random.seed(42)
    noise1 = np.random.randint(-20, 20, (h, w, 3))
    noise2 = np.random.randint(-20, 20, (h, w, 3))
    
    img1 = np.clip(img1.astype(int) + noise1, 0, 255).astype(np.uint8)
    img2 = np.clip(img2.astype(int) + noise2, 0, 255).astype(np.uint8)
    
    # Add some geometric features
    cv2.rectangle(img1, (50, 50), (100, 100), (255, 255, 255), 2)
    cv2.circle(img2, (150, 150), 30, (255, 255, 255), 2)
    
    # Test seam finding
    seam_finder = SeamFinder(blend_width=30)
    
    # Define overlap region (middle portion)
    overlap_region = (50, 150, 50, 150)
    
    # Test horizontal seam
    print("  Testing horizontal seam...")
    seam_mask_h = seam_finder.find_optimal_seam(img1, img2, overlap_region, 'horizontal')
    print(f"    Horizontal seam mask shape: {seam_mask_h.shape}")
    print(f"    Mask range: {seam_mask_h.min():.3f} - {seam_mask_h.max():.3f}")
    
    # Test vertical seam
    print("  Testing vertical seam...")
    seam_mask_v = seam_finder.find_optimal_seam(img1, img2, overlap_region, 'vertical')
    print(f"    Vertical seam mask shape: {seam_mask_v.shape}")
    print(f"    Mask range: {seam_mask_v.min():.3f} - {seam_mask_v.max():.3f}")
    
    # Save test results
    test_dir = "seam_test_results"
    os.makedirs(test_dir, exist_ok=True)
    
    cv2.imwrite(os.path.join(test_dir, "test_img1.png"), img1)
    cv2.imwrite(os.path.join(test_dir, "test_img2.png"), img2)
    
    # Visualize seam masks
    mask_vis_h = (seam_mask_h * 255).astype(np.uint8)
    mask_vis_v = (seam_mask_v * 255).astype(np.uint8)
    
    cv2.imwrite(os.path.join(test_dir, "seam_mask_horizontal.png"), mask_vis_h)
    cv2.imwrite(os.path.join(test_dir, "seam_mask_vertical.png"), mask_vis_v)
    
    # Create blended result
    y1, y2, x1, x2 = overlap_region
    region1 = img1[y1:y2, x1:x2]
    region2 = img2[y1:y2, x1:x2]
    
    # Blend using horizontal seam
    blended_h = region1.astype(float) * seam_mask_h[:,:,np.newaxis] + \
                region2.astype(float) * (1 - seam_mask_h[:,:,np.newaxis])
    blended_h = blended_h.astype(np.uint8)
    
    cv2.imwrite(os.path.join(test_dir, "blended_horizontal.png"), blended_h)
    
    print(f"  ✅ Test results saved to {test_dir}/")
    return True

def test_overlap_detection():
    """Test overlap region detection"""
    print("🧪 Testing Overlap Detection...")
    
    # Simulate grid positions
    rows, cols = 3, 3
    tile_h, tile_w = 100, 100
    overlap_pct = 0.3
    
    positions = []
    for i in range(rows * cols):
        row = i // cols
        col = i % cols
        
        # Snake pattern
        if row % 2 == 1:
            col = cols - 1 - col
        
        pos_y = row * tile_h * (1 - overlap_pct)
        pos_x = col * tile_w * (1 - overlap_pct)
        positions.append((pos_y, pos_x))
    
    print(f"  Grid: {rows}x{cols}, Overlap: {overlap_pct*100}%")
    print(f"  Positions: {len(positions)}")
    
    # Find overlaps
    overlap_regions = create_overlap_regions(positions, (tile_h, tile_w), overlap_pct)
    
    print(f"  Found {len(overlap_regions)} overlap regions:")
    for i, overlap in enumerate(overlap_regions):
        img1_idx = overlap['img1_idx']
        img2_idx = overlap['img2_idx']
        direction = overlap['direction']
        region = overlap['region']
        print(f"    {i+1}: Image {img1_idx} <-> {img2_idx} ({direction}) Region: {region}")
    
    return len(overlap_regions) > 0

def test_cost_computation():
    """Test cost map computation"""
    print("🧪 Testing Cost Map Computation...")
    
    # Create test images with different characteristics
    h, w = 100, 100
    
    # Image with strong edges
    img1 = np.zeros((h, w), dtype=np.uint8)
    cv2.rectangle(img1, (20, 20), (80, 80), 255, -1)
    cv2.rectangle(img1, (30, 30), (70, 70), 0, -1)
    
    # Image with texture
    img2 = np.random.randint(0, 255, (h, w), dtype=np.uint8)
    img2 = cv2.GaussianBlur(img2, (5, 5), 2)
    
    # Convert to color
    img1_color = cv2.cvtColor(img1, cv2.COLOR_GRAY2BGR)
    img2_color = cv2.cvtColor(img2, cv2.COLOR_GRAY2BGR)
    
    seam_finder = SeamFinder()
    
    # Test cost map computation
    cost_map = seam_finder._compute_cost_map(img1_color, img2_color)
    
    print(f"  Cost map shape: {cost_map.shape}")
    print(f"  Cost range: {cost_map.min():.3f} - {cost_map.max():.3f}")
    print(f"  Cost mean: {cost_map.mean():.3f}")
    
    # Save visualization
    test_dir = "seam_test_results"
    os.makedirs(test_dir, exist_ok=True)
    
    cost_vis = (cost_map * 255 / cost_map.max()).astype(np.uint8)
    cv2.imwrite(os.path.join(test_dir, "cost_map.png"), cost_vis)
    cv2.imwrite(os.path.join(test_dir, "cost_test_img1.png"), img1_color)
    cv2.imwrite(os.path.join(test_dir, "cost_test_img2.png"), img2_color)
    
    return True

def run_all_tests():
    """Run all seam finding tests"""
    print("🚀 Starting Seam Finding Tests...")
    print("="*50)
    
    tests = [
        ("Basic Seam Finding", test_seam_finder_basic),
        ("Overlap Detection", test_overlap_detection),
        ("Cost Computation", test_cost_computation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n📋 {test_name}")
            print("-" * 30)
            result = test_func()
            results.append((test_name, result, None))
            print(f"✅ {test_name}: PASSED")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"❌ {test_name}: FAILED - {e}")
            import traceback
            traceback.print_exc()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, error in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if error:
            print(f"      Error: {error}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Seam finding is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
