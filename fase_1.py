import os
import glob
import cv2
import numpy as np
import tifffile
from PIL import Image

# --- FASE 1 & 2 (Disederhanakan): Hitung Posisi Grid dan <PERSON> ---
def get_image_positions(folder_path, rows, cols, overlap_pct):
    print("Mencari posisi gambar...")
    
    image_files = sorted(glob.glob(os.path.join(folder_path, "*.png")) + glob.glob(os.path.join(folder_path, "*.jpg")))
    if not image_files: return None, None
    
    first_img = cv2.imread(image_files[0], cv2.IMREAD_GRAYSCALE)
    if first_img is None: return None, None
    h, w = first_img.shape[:2]

    positions = {}
    for i in range(len(image_files)):
        current_row = i // cols
        current_col = i % cols
        if current_row % 2 == 1:
            current_col = cols - 1 - current_col
        display_row = rows - 1 - current_row

        # Hitung posisi grid dasar
        pos_x_base = current_col * w * (1 - overlap_pct)
        pos_y_base = display_row * h * (1 - overlap_pct)
        
        # Simpan posisi dasar
        positions[i] = [pos_x_base, pos_y_base]

    # Tambahkan koreksi dari Phase Correlation
    print("Menambahkan koreksi halus dari Phase Correlation...")
    for i in range(len(image_files)):
        current_row = i // cols
        current_col = i % cols
        if current_row % 2 == 1: current_col = cols - 1 - current_col

        # Koreksi Horizontal
        if cols < cols - 1:
            neighbor_idx = current_row * cols + (current_col + 1)
            dx, dy = compute_phase_correlation(image_files[i], image_files[neighbor_idx])
            positions[neighbor_idx][0] += dx
            positions[neighbor_idx][1] += dy

        # Koreksi Vertikal
        if rows < rows - 1:
            neighbor_idx = (current_row + 1) * cols + current_col
            dx, dy = compute_phase_correlation(image_files[i], image_files[neighbor_idx])
            positions[neighbor_idx][0] += dx
            positions[neighbor_idx][1] += dy
            
    return image_files, positions

def compute_phase_correlation(img1_path, img2_path):
    img1 = cv2.imread(img1_path, cv2.IMREAD_GRAYSCALE)
    img2 = cv2.imread(img2_path, cv2.IMREAD_GRAYSCALE)
    if img1 is None or img2 is None: return 0, 0
    img1_float = np.float32(img1)
    img2_float = np.float32(img2)
    rows, cols = img1_float.shape
    img2_float = cv2.resize(img2_float, (cols, rows))
    output, _ = cv2.phaseCorrelate(img1_float, img2_float)
    return output

# --- FASE 3: Penyambungan Gambar Final ---
def create_final_mosaic(image_files, positions, rows, cols):
    print("Memulai Fase 3: Penyambungan Final...")
    
    first_img = cv2.imread(image_files[0])
    h, w = first_img.shape[:2]

    min_x = min(pos[0] for pos in positions.values())
    max_x = max(pos[0] + w for pos in positions.values())
    min_y = min(pos[1] for pos in positions.values())
    max_y = max(pos[1] + h for pos in positions.values())

    mosaic_w = int(max_x - min_x)
    mosaic_h = int(max_y - min_y)
    
    final_mosaic = np.zeros((mosaic_h, mosaic_w, 3), dtype=np.float32)
    weight_sum = np.zeros((mosaic_h, mosaic_w), dtype=np.float32)
    
    # Buat weight map (peta bobot) yang halus untuk perpaduan yang baik
    tile_weight = np.ones((h, w), dtype=np.float32)
    
    # Perbaiki ukuran kernel agar selalu ganjil dan positif
    kernel_w = (w // 4) * 2 + 1
    kernel_h = (h // 4) * 2 + 1
    
    tile_weight = cv2.GaussianBlur(tile_weight, (kernel_w, kernel_h), 0)
    
    for i in range(len(image_files)):
        img = cv2.imread(image_files[i])
        
        pos_x = int(positions[i][0] - min_x)
        pos_y = int(positions[i][1] - min_y)

        h_part, w_part = img.shape[:2]
        final_mosaic[pos_y:pos_y+h_part, pos_x:pos_x+w_part] += img.astype(np.float32) * tile_weight[:,:,np.newaxis]
        weight_sum[pos_y:pos_y+h_part, pos_x:pos_x+w_part] += tile_weight

    weight_sum[weight_sum == 0] = 1.0
    final_mosaic = (final_mosaic / weight_sum[:, :, np.newaxis]).astype(np.uint8)
    
    output_path = os.path.join(os.path.dirname(image_files[0]), "final_mosaic_reliable.tif")
    tifffile.imwrite(output_path, final_mosaic)

    print(f"\nPenyambungan Selesai. Mozaik tersimpan di: {output_path}")

# --- Alur Utama ---
if __name__ == "__main__":
    folder_path = r"D:\Stitch\bilinear_stitching_20250915_152529"
    rows = 4
    cols = 9
    overlap_pct = 0.2
    
    files, positions = get_image_positions(folder_path, rows, cols, overlap_pct)
    if files and positions:
        create_final_mosaic(files, positions, rows, cols)