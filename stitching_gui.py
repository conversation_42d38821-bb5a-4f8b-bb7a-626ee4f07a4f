#!/usr/bin/env python3
"""
GUI Interface for Microscopic Image Stitching
Features:
- File selection and validation
- Tile connection graph visualization
- Configurable stitching parameters
- Progress monitoring
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import re
import threading
from pathlib import Path
import numpy as np
import cv2
import networkx as nx

# Import stitching modules
try:
    from stitching.feature_detector import FeatureDetector
    from stitching.feature_matcher import FeatureMatcher
    STITCHING_AVAILABLE = True
except ImportError:
    STITCHING_AVAILABLE = False
    print("Warning: stitching library not available. Some features will be disabled.")

class TileInfo:
    """Class to store tile information"""
    def __init__(self, filename, row, col, path):
        self.filename = filename
        self.row = row
        self.col = col
        self.path = path
        self.connections = []
        self.matches = {}

class StitchingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Microscopic Image Stitching GUI")
        self.root.geometry("1200x800")
        
        # Data storage
        self.tiles = {}  # {(row, col): TileInfo}
        self.tile_files = []
        self.matches_graph = nx.Graph()
        self.selected_folder = ""
        
        # Create GUI
        self.create_widgets()
        
    def create_widgets(self):
        """Create the main GUI widgets"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔬 Microscopic Image Stitching", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="📁 File Selection", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="Select Folder", 
                  command=self.select_folder).grid(row=0, column=0, padx=(0, 10))
        
        self.folder_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.folder_var, 
                 state="readonly").grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(file_frame, text="Analyze Files", 
                  command=self.analyze_files).grid(row=0, column=2)
        
        # Info frame
        info_frame = ttk.LabelFrame(main_frame, text="📊 File Information", padding="10")
        info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(1, weight=1)
        
        self.info_text = scrolledtext.ScrolledText(info_frame, width=40, height=20)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Graph frame
        graph_frame = ttk.LabelFrame(main_frame, text="🔗 Tile Connections", padding="10")
        graph_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        graph_frame.columnconfigure(0, weight=1)
        graph_frame.rowconfigure(1, weight=1)
        
        ttk.Button(graph_frame, text="Generate Matches Graph", 
                  command=self.generate_matches_graph).grid(row=0, column=0, pady=(0, 10))
        
        self.graph_text = scrolledtext.ScrolledText(graph_frame, width=50, height=20)
        self.graph_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Control frame
        control_frame = ttk.LabelFrame(main_frame, text="⚙️ Stitching Controls", padding="10")
        control_frame.grid(row=2, column=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        control_frame.columnconfigure(0, weight=1)
        
        # Parameters
        ttk.Label(control_frame, text="Overlap Threshold:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.overlap_var = tk.DoubleVar(value=0.3)
        ttk.Scale(control_frame, from_=0.1, to=0.8, variable=self.overlap_var, 
                 orient=tk.HORIZONTAL).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(control_frame, text="Min Matches:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.min_matches_var = tk.IntVar(value=10)
        ttk.Spinbox(control_frame, from_=5, to=100, textvariable=self.min_matches_var, 
                   width=10).grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        
        # Stitching method
        ttk.Label(control_frame, text="Stitching Method:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.method_var = tk.StringVar(value="seam_finding")
        method_combo = ttk.Combobox(control_frame, textvariable=self.method_var, 
                                   values=["seam_finding", "phase_correlation", "feature_based"])
        method_combo.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        method_combo.state(['readonly'])
        
        # Buttons
        ttk.Button(control_frame, text="🔍 Validate Connections", 
                  command=self.validate_connections).grid(row=6, column=0, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(control_frame, text="🧩 Start Stitching", 
                  command=self.start_stitching).grid(row=7, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # Progress
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(control_frame, textvariable=self.progress_var).grid(row=8, column=0, pady=10)
        
        self.progress_bar = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress_bar.grid(row=9, column=0, sticky=(tk.W, tk.E), pady=5)
        
    def select_folder(self):
        """Select folder containing tile images"""
        folder = filedialog.askdirectory(title="Select folder with tile images")
        if folder:
            self.selected_folder = folder
            self.folder_var.set(folder)
            self.info_text.delete(1.0, tk.END)
            self.graph_text.delete(1.0, tk.END)
            
    def analyze_files(self):
        """Analyze files in selected folder"""
        if not self.selected_folder:
            messagebox.showerror("Error", "Please select a folder first")
            return
            
        self.progress_var.set("Analyzing files...")
        self.progress_bar.start()
        
        # Run analysis in thread to prevent GUI freezing
        thread = threading.Thread(target=self._analyze_files_thread)
        thread.daemon = True
        thread.start()
        
    def _analyze_files_thread(self):
        """Thread function for file analysis"""
        try:
            # Find tile files
            tile_pattern = re.compile(r'tile_(\d+)_(\d+)\.(png|jpg|jpeg)', re.IGNORECASE)
            self.tiles = {}
            self.tile_files = []

            for file_path in Path(self.selected_folder).rglob("*"):
                if file_path.is_file():
                    match = tile_pattern.match(file_path.name)
                    if match:
                        row, col, _ = match.groups()  # ext not used
                        row, col = int(row), int(col)

                        tile_info = TileInfo(file_path.name, row, col, str(file_path))
                        self.tiles[(row, col)] = tile_info
                        self.tile_files.append(str(file_path))

            # Update GUI in main thread
            self.root.after(0, self._update_file_info)

        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            self.root.after(0, lambda msg=error_msg: messagebox.showerror("Error", msg))
        finally:
            self.root.after(0, lambda: self.progress_bar.stop())
            
    def _update_file_info(self):
        """Update file information display"""
        self.info_text.delete(1.0, tk.END)
        
        if not self.tiles:
            self.info_text.insert(tk.END, "No tile files found!\n\n")
            self.info_text.insert(tk.END, "Expected format: tile_XX_YY.png\n")
            self.info_text.insert(tk.END, "Where XX = row, YY = column")
            self.progress_var.set("No files found")
            return
            
        # Display file information
        self.info_text.insert(tk.END, f"📁 Found {len(self.tiles)} tile files\n\n")
        
        # Grid dimensions
        rows = [tile.row for tile in self.tiles.values()]
        cols = [tile.col for tile in self.tiles.values()]
        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)
        
        self.info_text.insert(tk.END, f"📐 Grid dimensions:\n")
        self.info_text.insert(tk.END, f"   Rows: {min_row} to {max_row} ({max_row - min_row + 1} rows)\n")
        self.info_text.insert(tk.END, f"   Cols: {min_col} to {max_col} ({max_col - min_col + 1} cols)\n\n")
        
        # List files
        self.info_text.insert(tk.END, "📋 Tile files:\n")
        for (row, col), tile in sorted(self.tiles.items()):
            self.info_text.insert(tk.END, f"   {tile.filename} -> ({row}, {col})\n")
            
        self.progress_var.set(f"Found {len(self.tiles)} tiles")
        
    def generate_matches_graph(self):
        """Generate matches graph between adjacent tiles"""
        if not self.tiles:
            messagebox.showerror("Error", "Please analyze files first")
            return
            
        if not STITCHING_AVAILABLE:
            messagebox.showerror("Error", "Stitching library not available")
            return
            
        self.progress_var.set("Generating matches graph...")
        self.progress_bar.start()
        
        # Run in thread
        thread = threading.Thread(target=self._generate_matches_thread)
        thread.daemon = True
        thread.start()
        
    def _generate_matches_thread(self):
        """Thread function for generating matches"""
        try:
            self.matches_graph = nx.Graph()

            # Simple adjacency-based matching (fallback if stitching library not available)
            if not STITCHING_AVAILABLE:
                matches_info = self._generate_simple_adjacency_graph()
            else:
                matches_info = self._generate_feature_based_graph()

            # Update GUI
            self.root.after(0, lambda: self._update_matches_display(matches_info))

        except Exception as e:
            error_msg = f"Match generation failed: {str(e)}"
            self.root.after(0, lambda msg=error_msg: messagebox.showerror("Error", msg))
        finally:
            self.root.after(0, lambda: self.progress_bar.stop())

    def _generate_simple_adjacency_graph(self):
        """Generate simple adjacency-based connections"""
        matches_info = []

        for (row, col), tile in self.tiles.items():
            # Check only direct neighbors (not diagonal)
            adjacent_positions = [
                (row, col + 1),      # Right
                (row + 1, col),      # Bottom
            ]

            for adj_pos in adjacent_positions:
                if adj_pos in self.tiles:
                    adj_tile = self.tiles[adj_pos]

                    # Create mock match data
                    matches_count = np.random.randint(15, 50)  # Simulated matches
                    good_matches = int(matches_count * 0.7)
                    quality = good_matches / matches_count

                    # Add to graph
                    self.matches_graph.add_edge(
                        tile.filename, adj_tile.filename,
                        matches=matches_count,
                        good_matches=good_matches,
                        quality=quality
                    )

                    matches_info.append({
                        'tile1': tile.filename,
                        'tile2': adj_tile.filename,
                        'matches': matches_count,
                        'good_matches': good_matches,
                        'quality': quality
                    })

        return matches_info

    def _generate_feature_based_graph(self):
        """Generate feature-based matches using stitching library"""
        # Initialize feature detector and matcher
        detector = FeatureDetector()
        matcher = FeatureMatcher()

        # Load images and detect features
        images_data = {}
        for (row, col), tile in self.tiles.items():
            img = cv2.imread(tile.path)
            if img is not None:
                # Detect features
                keypoints, descriptors = detector.detect_and_compute(img)
                images_data[(row, col)] = {
                    'image': img,
                    'keypoints': keypoints,
                    'descriptors': descriptors,
                    'tile': tile
                }

        # Find matches between adjacent tiles
        matches_info = []

        for (row, col), data in images_data.items():
            # Check adjacent tiles (right, bottom only for proper grid)
            adjacent_positions = [
                (row, col + 1),      # Right
                (row + 1, col),      # Bottom
            ]

            for adj_pos in adjacent_positions:
                if adj_pos in images_data:
                    # Find matches
                    matches = matcher.match_features(
                        data['descriptors'],
                        images_data[adj_pos]['descriptors']
                    )

                    if matches and len(matches) >= self.min_matches_var.get():
                        # Calculate match quality
                        good_matches = [m for m in matches if m.distance < 0.7]
                        match_quality = len(good_matches) / len(matches) if matches else 0

                        # Add to graph
                        tile1_name = data['tile'].filename
                        tile2_name = images_data[adj_pos]['tile'].filename

                        self.matches_graph.add_edge(
                            tile1_name, tile2_name,
                            matches=len(matches),
                            good_matches=len(good_matches),
                            quality=match_quality
                        )

                        matches_info.append({
                            'tile1': tile1_name,
                            'tile2': tile2_name,
                            'matches': len(matches),
                            'good_matches': len(good_matches),
                            'quality': match_quality
                        })

        return matches_info
            
    def _update_matches_display(self, matches_info):
        """Update matches graph display"""
        self.graph_text.delete(1.0, tk.END)
        
        if not matches_info:
            self.graph_text.insert(tk.END, "No matches found between adjacent tiles!\n\n")
            self.graph_text.insert(tk.END, "This could indicate:\n")
            self.graph_text.insert(tk.END, "- Insufficient overlap between tiles\n")
            self.graph_text.insert(tk.END, "- Poor image quality\n")
            self.graph_text.insert(tk.END, "- Incorrect tile arrangement\n")
            self.progress_var.set("No matches found")
            return
            
        # Display matches in graph format
        self.graph_text.insert(tk.END, f"🔗 Found {len(matches_info)} tile connections:\n\n")
        
        for match in matches_info:
            # Format similar to the example you provided
            line = f'"{match["tile1"]}" -- "{match["tile2"]}"'
            line += f'[label="Nm={match["matches"]}, Ni={match["good_matches"]}, C={match["quality"]:.5f}"];\n'
            self.graph_text.insert(tk.END, line)
            
        self.progress_var.set(f"Found {len(matches_info)} connections")
        
    def validate_connections(self):
        """Validate tile connections for proper grid structure"""
        if not self.matches_graph.nodes():
            messagebox.showerror("Error", "Please generate matches graph first")
            return
            
        # Check connectivity
        connected_components = list(nx.connected_components(self.matches_graph))
        
        validation_text = "🔍 Connection Validation:\n\n"
        
        if len(connected_components) == 1:
            validation_text += "✅ All tiles are connected in a single component\n"
        else:
            validation_text += f"⚠️  Found {len(connected_components)} separate components:\n"
            for i, component in enumerate(connected_components):
                validation_text += f"   Component {i+1}: {len(component)} tiles\n"
                
        # Check for isolated tiles
        isolated_tiles = [node for node in self.matches_graph.nodes() 
                         if self.matches_graph.degree(node) == 0]
        if isolated_tiles:
            validation_text += f"\n❌ Isolated tiles (no connections): {len(isolated_tiles)}\n"
            for tile in isolated_tiles:
                validation_text += f"   - {tile}\n"
        else:
            validation_text += "\n✅ No isolated tiles found\n"
            
        # Show validation results
        messagebox.showinfo("Validation Results", validation_text)
        
    def start_stitching(self):
        """Start the stitching process"""
        if not self.matches_graph.nodes():
            messagebox.showerror("Error", "Please generate and validate connections first")
            return
            
        # Check if we have connected components
        connected_components = list(nx.connected_components(self.matches_graph))
        
        if len(connected_components) > 1:
            result = messagebox.askyesno(
                "Multiple Components", 
                f"Found {len(connected_components)} separate tile groups. "
                "This will create multiple output images. Continue?"
            )
            if not result:
                return
                
        self.progress_var.set("Starting stitching...")
        self.progress_bar.start()
        
        # Run stitching in thread
        thread = threading.Thread(target=self._stitching_thread)
        thread.daemon = True
        thread.start()
        
    def _stitching_thread(self):
        """Thread function for stitching process"""
        try:
            method = self.method_var.get()

            if method == "seam_finding":
                self._stitch_with_seam_finding()
            elif method == "phase_correlation":
                self._stitch_with_phase_correlation()
            else:
                self._stitch_with_feature_based()

        except Exception as e:
            error_msg = f"Stitching failed: {str(e)}"
            self.root.after(0, lambda msg=error_msg: messagebox.showerror("Error", msg))
        finally:
            self.root.after(0, lambda: self.progress_bar.stop())
            
    def _stitch_with_seam_finding(self):
        """Stitch using seam finding method"""
        # Import seam finding modules
        from run_configurable_seam_stitching import run_with_config
        from seam_config import SeamPresets
        
        # Use grid elimination preset
        config = SeamPresets.grid_elimination_focus()
        
        # Determine grid parameters
        rows = [tile.row for tile in self.tiles.values()]
        cols = [tile.col for tile in self.tiles.values()]
        grid_rows = max(rows) - min(rows) + 1
        grid_cols = max(cols) - min(cols) + 1
        
        # Run stitching
        success = run_with_config(config, self.selected_folder, grid_rows, grid_cols, 0.2)
        
        if success:
            self.root.after(0, lambda: self.progress_var.set("Stitching completed!"))
            self.root.after(0, lambda: messagebox.showinfo("Success", "Stitching completed successfully!"))
        else:
            self.root.after(0, lambda: self.progress_var.set("Stitching failed"))
            
    def _stitch_with_phase_correlation(self):
        """Stitch using phase correlation method"""
        # Placeholder for phase correlation stitching
        self.root.after(0, lambda: self.progress_var.set("Phase correlation not implemented yet"))
        
    def _stitch_with_feature_based(self):
        """Stitch using feature-based method"""
        # Placeholder for feature-based stitching
        self.root.after(0, lambda: self.progress_var.set("Feature-based not implemented yet"))

def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    StitchingGUI(root)  # Create GUI instance
    root.mainloop()

if __name__ == "__main__":
    main()
