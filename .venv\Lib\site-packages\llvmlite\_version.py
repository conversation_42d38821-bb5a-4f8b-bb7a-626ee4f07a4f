
# This file was generated by 'versioneer.py' (0.14) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

version_version = '0.45.0'
version_full = '2d479c9ce4259c94f388ead34f428a71be246ace'
def get_versions(default={}, verbose=False):
    return {'version': version_version, 'full': version_full}

