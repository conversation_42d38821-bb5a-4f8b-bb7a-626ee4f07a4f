#!/usr/bin/env python3
"""
Test script for GUI functions
Tests core functionality without requiring GUI interaction
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import numpy as np
import cv2

def create_test_tiles(output_dir, grid_size=(2, 3)):
    """Create test tiles for GUI testing"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    tile_width, tile_height = 100, 100
    
    for row in range(grid_size[0]):
        for col in range(grid_size[1]):
            # Create simple test image
            img = np.zeros((tile_height, tile_width, 3), dtype=np.uint8)
            
            # Add unique pattern
            color = [(row * 100) % 255, (col * 100) % 255, ((row + col) * 80) % 255]
            img[:, :] = color
            
            # Add some features
            cv2.rectangle(img, (10, 10), (90, 90), (255, 255, 255), 2)
            cv2.circle(img, (50, 50), 20, (0, 0, 0), 2)
            
            # Add text
            text = f"{row},{col}"
            cv2.putText(img, text, (30, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Save
            filename = f"tile_{row:02d}_{col:02d}.png"
            filepath = os.path.join(output_dir, filename)
            cv2.imwrite(filepath, img)
    
    return output_dir

def test_tile_info_class():
    """Test TileInfo class"""
    print("🧪 Testing TileInfo class...")
    
    from stitching_gui import TileInfo
    
    tile = TileInfo("tile_01_02.png", 1, 2, "/path/to/tile_01_02.png")
    
    assert tile.filename == "tile_01_02.png"
    assert tile.row == 1
    assert tile.col == 2
    assert tile.path == "/path/to/tile_01_02.png"
    assert tile.connections == []
    assert tile.matches == {}
    
    print("✅ TileInfo class test passed")

def test_file_analysis():
    """Test file analysis functionality"""
    print("🧪 Testing file analysis...")
    
    # Create temporary test directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test tiles
        create_test_tiles(temp_dir, (2, 3))
        
        # Test file pattern matching
        from stitching_gui import StitchingGUI
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        gui = StitchingGUI(root)
        gui.selected_folder = temp_dir
        
        # Run analysis
        gui._analyze_files_thread()
        
        # Check results
        assert len(gui.tiles) == 6  # 2x3 grid
        assert (0, 0) in gui.tiles
        assert (1, 2) in gui.tiles
        assert len(gui.tile_files) == 6
        
        root.destroy()
    
    print("✅ File analysis test passed")

def test_adjacency_graph():
    """Test adjacency graph generation"""
    print("🧪 Testing adjacency graph generation...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        create_test_tiles(temp_dir, (2, 3))
        
        from stitching_gui import StitchingGUI
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        gui = StitchingGUI(root)
        gui.selected_folder = temp_dir
        
        # Analyze files first
        gui._analyze_files_thread()
        
        # Generate adjacency graph
        matches_info = gui._generate_simple_adjacency_graph()
        
        # Check results
        assert len(matches_info) > 0
        assert len(gui.matches_graph.nodes()) > 0
        assert len(gui.matches_graph.edges()) > 0
        
        # Check that only adjacent tiles are connected
        for match in matches_info:
            tile1 = match['tile1']
            tile2 = match['tile2']
            print(f"  Connection: {tile1} <-> {tile2}")
        
        root.destroy()
    
    print("✅ Adjacency graph test passed")

def test_validation():
    """Test connection validation"""
    print("🧪 Testing connection validation...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        create_test_tiles(temp_dir, (2, 3))
        
        from stitching_gui import StitchingGUI
        import tkinter as tk
        import networkx as nx
        
        root = tk.Tk()
        root.withdraw()
        
        gui = StitchingGUI(root)
        gui.selected_folder = temp_dir
        
        # Create a simple test graph
        gui.matches_graph = nx.Graph()
        gui.matches_graph.add_edge("tile_00_00.png", "tile_00_01.png")
        gui.matches_graph.add_edge("tile_00_01.png", "tile_00_02.png")
        gui.matches_graph.add_edge("tile_00_00.png", "tile_01_00.png")
        
        # Test connectivity
        connected_components = list(nx.connected_components(gui.matches_graph))
        assert len(connected_components) >= 1
        
        root.destroy()
    
    print("✅ Connection validation test passed")

def test_gui_creation():
    """Test GUI creation without errors"""
    print("🧪 Testing GUI creation...")
    
    from stitching_gui import StitchingGUI
    import tkinter as tk
    
    root = tk.Tk()
    root.withdraw()  # Hide window
    
    try:
        gui = StitchingGUI(root)
        
        # Check that main components exist
        assert hasattr(gui, 'tiles')
        assert hasattr(gui, 'matches_graph')
        assert hasattr(gui, 'selected_folder')
        assert hasattr(gui, 'info_text')
        assert hasattr(gui, 'graph_text')
        
        print("✅ GUI creation test passed")
        
    finally:
        root.destroy()

def test_matches_display_format():
    """Test matches display format"""
    print("🧪 Testing matches display format...")
    
    from stitching_gui import StitchingGUI
    import tkinter as tk
    
    root = tk.Tk()
    root.withdraw()
    
    gui = StitchingGUI(root)
    
    # Create test matches info
    test_matches = [
        {
            'tile1': 'tile_00_00.png',
            'tile2': 'tile_00_01.png',
            'matches': 33,
            'good_matches': 23,
            'quality': 1.28492
        },
        {
            'tile1': 'tile_00_01.png', 
            'tile2': 'tile_01_01.png',
            'matches': 28,
            'good_matches': 19,
            'quality': 0.67857
        }
    ]
    
    # Test display update
    gui._update_matches_display(test_matches)
    
    # Check that text was added
    content = gui.graph_text.get(1.0, tk.END)
    assert 'tile_00_00.png' in content
    assert 'Nm=33' in content
    assert 'Ni=23' in content
    assert 'C=1.28492' in content
    
    root.destroy()
    
    print("✅ Matches display format test passed")

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting GUI Function Tests")
    print("=" * 50)
    
    tests = [
        test_tile_info_class,
        test_gui_creation,
        test_file_analysis,
        test_adjacency_graph,
        test_validation,
        test_matches_display_format
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} failed: {e}")
            failed += 1
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! GUI is ready to use.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
