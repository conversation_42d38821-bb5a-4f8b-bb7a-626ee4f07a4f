import os
import glob
import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np

class StitchingViewer:
    def __init__(self, master):
        self.master = master
        master.title("Stitching Viewer")

        self.controls_frame = tk.Frame(master)
        self.controls_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        tk.Label(self.controls_frame, text="Folder:").pack(side=tk.LEFT, padx=(0, 5))
        self.folder_path = tk.Entry(self.controls_frame, width=30)
        self.folder_path.pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(self.controls_frame, text="Browse", command=self.browse_folder).pack(side=tk.LEFT)

        tk.Label(self.controls_frame, text="Rows:").pack(side=tk.LEFT, padx=(10, 5))
        self.row_entry = tk.Entry(self.controls_frame, width=5)
        self.row_entry.insert(0, "4")
        self.row_entry.pack(side=tk.LEFT)

        tk.Label(self.controls_frame, text="Columns:").pack(side=tk.LEFT, padx=(10, 5))
        self.col_entry = tk.Entry(self.controls_frame, width=5)
        self.col_entry.insert(0, "9")
        self.col_entry.pack(side=tk.LEFT)

        tk.Label(self.controls_frame, text="Overlap(%):").pack(side=tk.LEFT, padx=(10, 5))
        self.overlap_entry = tk.Entry(self.controls_frame, width=5)
        self.overlap_entry.insert(0, "20")
        self.overlap_entry.pack(side=tk.LEFT)

        self.show_overlap_var = tk.IntVar()
        tk.Checkbutton(self.controls_frame, text="Show Overlap", variable=self.show_overlap_var, command=self.display_images).pack(side=tk.LEFT, padx=(10, 0))

        tk.Button(self.controls_frame, text="Tampilkan Gambar", command=self.display_images).pack(side=tk.LEFT, padx=(10, 0))
        
        tk.Button(self.controls_frame, text="Analyze", command=self.analyze_images).pack(side=tk.LEFT, padx=(10, 0))

        self.canvas = tk.Canvas(master, bg="grey")
        self.canvas.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True)
        
        self.image_files = []
        self.rows = 0
        self.cols = 0

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_path.delete(0, tk.END)
            self.folder_path.insert(0, folder)
            self.display_images()

    def display_images(self):
        self.canvas.delete("all")

        try:
            self.rows = int(self.row_entry.get())
            self.cols = int(self.col_entry.get())
            overlap_pct = int(self.overlap_entry.get()) / 100.0
            folder = self.folder_path.get()
        except ValueError:
            messagebox.showerror("Error", "Input harus berupa angka.")
            return

        self.image_files = sorted(glob.glob(os.path.join(folder, "*.png")) + glob.glob(os.path.join(folder, "*.jpg")))
        if not self.image_files:
            messagebox.showerror("Error", "Tidak ada file gambar ditemukan.")
            return

        if len(self.image_files) != self.rows * self.cols:
            messagebox.showwarning("Peringatan", f"Jumlah gambar ({len(self.image_files)}) tidak cocok dengan R x C ({self.rows}x{self.cols}).")

        first_img = Image.open(self.image_files[0])
        tile_size = first_img.size

        if self.show_overlap_var.get():
            mosaic_width = tile_size[0] + (self.cols - 1) * tile_size[0] * (1 - overlap_pct)
            mosaic_height = tile_size[1] + (self.rows - 1) * tile_size[1] * (1 - overlap_pct)
        else:
            mosaic_width = self.cols * tile_size[0]
            mosaic_height = self.rows * tile_size[1]

        full_mosaic = Image.new("RGB", (int(mosaic_width), int(mosaic_height)), (128, 128, 128))
        
        for i, filepath in enumerate(self.image_files):
            current_row = i // self.cols
            current_col = i % self.cols
            if current_row % 2 == 1:
                current_col = self.cols - 1 - current_col
            display_row = self.rows - 1 - current_row

            if self.show_overlap_var.get():
                pos_x = int(current_col * tile_size[0] * (1 - overlap_pct))
                pos_y = int(display_row * tile_size[1] * (1 - overlap_pct))
            else:
                pos_x = int(current_col * tile_size[0])
                pos_y = int(display_row * tile_size[1])
            
            try:
                img = Image.open(filepath)
                full_mosaic.paste(img, (pos_x, pos_y))
            except Exception as e:
                print(f"Gagal memproses file {filepath}: {e}")

        self.master.update_idletasks()

        canvas_w, canvas_h = self.canvas.winfo_width(), self.canvas.winfo_height()
        scale_factor = min((canvas_w - 10) / full_mosaic.width, (canvas_h - 10) / full_mosaic.height)
        new_w = int(full_mosaic.width * scale_factor)
        new_h = int(full_mosaic.height * scale_factor)
        
        resized_mosaic = full_mosaic.resize((new_w, new_h), Image.Resampling.LANCZOS)
        self.photo_image = ImageTk.PhotoImage(resized_mosaic)
        self.canvas.create_image(canvas_w / 2, canvas_h / 2, image=self.photo_image, anchor=tk.CENTER)
        
    def analyze_images(self):
        if not self.image_files:
            messagebox.showwarning("Peringatan", "Silakan tampilkan gambar terlebih dahulu.")
            return

        self.canvas.delete("analysis_lines")
        print("\n" + "="*50)
        print("Mulai Analisis Korelasi Gambar")
        print("="*50)

        MIN_MATCH_COUNT = 100

        for row in range(self.rows):
            for col in range(self.cols):
                current_idx = row * self.cols + col
                if row % 2 == 1:
                    current_idx = row * self.cols + (self.cols - 1 - col)

                # --- Analisis Horizontal (Tetangga Kanan) ---
                if col < self.cols - 1:
                    neighbor_col = col + 1
                    neighbor_row = row
                    neighbor_idx = neighbor_row * self.cols + neighbor_col
                    if neighbor_row % 2 == 1:
                        neighbor_idx = neighbor_row * self.cols + (self.cols - 1 - neighbor_col)

                    result = "Feature Detection" if self.check_feature_matches(self.image_files[current_idx], self.image_files[neighbor_idx], MIN_MATCH_COUNT) else "Phase Correlation (Fallback)"
                    print(f"Gambar {current_idx+1} -> Gambar {neighbor_idx+1}: {result}")

                # --- Analisis Vertikal (Tetangga Atas) ---
                if row < self.rows - 1:
                    neighbor_row = row + 1
                    neighbor_col = col
                    neighbor_idx = neighbor_row * self.cols + neighbor_col
                    if neighbor_row % 2 == 1:
                        neighbor_idx = neighbor_row * self.cols + (self.cols - 1 - neighbor_col)
                    
                    result = "Feature Detection" if self.check_feature_matches(self.image_files[current_idx], self.image_files[neighbor_idx], MIN_MATCH_COUNT) else "Phase Correlation (Fallback)"
                    print(f"Gambar {current_idx+1} -> Gambar {neighbor_idx+1}: {result}")

        print("\n" + "="*50)
        print("Analisis Selesai")
        print("="*50)
        messagebox.showinfo("Analisis Selesai", "Hasil analisis telah dicetak di konsol.")

    def check_feature_matches(self, path1, path2, min_matches):
        try:
            img1 = cv2.imread(path1, cv2.IMREAD_GRAYSCALE)
            img2 = cv2.imread(path2, cv2.IMREAD_GRAYSCALE)
            
            if img1 is None or img2 is None:
                return False

            orb = cv2.ORB_create(nfeatures=2000)
            kp1, des1 = orb.detectAndCompute(img1, None)
            kp2, des2 = orb.detectAndCompute(img2, None)

            if des1 is None or des2 is None or des1.shape[0] < min_matches or des2.shape[0] < min_matches:
                return False

            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = bf.match(des1, des2)
            
            return len(matches) >= min_matches

        except Exception as e:
            print(f"Error saat menganalisis {path1} dan {path2}: {e}")
            return False

root = tk.Tk()
app = StitchingViewer(root)
root.geometry("1000x800")
root.mainloop()