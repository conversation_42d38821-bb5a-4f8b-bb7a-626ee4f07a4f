import os
import glob
from PIL import Image, ImageDraw
import numpy as np

def visualize_analysis(folder_path, analysis_data, rows=4, cols=9, overlap_pct=0.2):
    """
    Membuat visualisasi mozaik gambar dengan garis-garis koneksi analisis.
    
    Args:
        folder_path (str): <PERSON><PERSON><PERSON> ke folder yang berisi gambar.
        analysis_data (list): Daftar string hasil analisis.
        rows (int): Jumlah baris grid.
        cols (int): Jumlah kolom grid.
        overlap_pct (float): Persentase tumpang tindih (0.0 - 1.0).
    """
    print("Mulai membuat visualisasi...")

    # Muat gambar dan dapatkan dimensinya
    image_files = sorted(glob.glob(os.path.join(folder_path, "*.png")) + glob.glob(os.path.join(folder_path, "*.jpg")))
    if not image_files:
        print("Error: Tidak ada file gambar ditemukan.")
        return
    
    first_img = Image.open(image_files[0])
    tile_w, tile_h = first_img.size

    # Hitung ukuran mozaik
    mosaic_w = int(tile_w + (cols - 1) * tile_w * (1 - overlap_pct))
    mosaic_h = int(tile_h + (rows - 1) * tile_h * (1 - overlap_pct))
    
    # Buat kanvas mozaik
    full_mosaic = Image.new("RGB", (mosaic_w, mosaic_h), (200, 200, 200))
    image_positions = {}
    
    # Posisi gambar pada mozaik dan simpan posisi pusatnya
    for i, filepath in enumerate(image_files):
        current_row = i // cols
        current_col = i % cols
        if current_row % 2 == 1:
            current_col = cols - 1 - current_col
        display_row = rows - 1 - current_row

        pos_x = int(current_col * tile_w * (1 - overlap_pct))
        pos_y = int(display_row * tile_h * (1 - overlap_pct))
        
        try:
            img = Image.open(filepath)
            full_mosaic.paste(img, (pos_x, pos_y))
            
            # Simpan posisi pusat ubin
            center_x = pos_x + tile_w // 2
            center_y = pos_y + tile_h // 2
            image_positions[i + 1] = (center_x, center_y)
        except Exception as e:
            print(f"Gagal memproses file {filepath}: {e}")

    # Siapkan untuk menggambar garis
    draw = ImageDraw.Draw(full_mosaic)

    # Parse dan visualisasikan data analisis
    for line in analysis_data:
        parts = line.replace("Gambar ", "").replace(":", "").split()
        if len(parts) >= 3:
            img1_id = int(parts[0])
            img2_id = int(parts[2])
            method = parts[3]
            
            if method == "Feature":
                color = "green"
            else:
                color = "red"
            
            if img1_id in image_positions and img2_id in image_positions:
                pos1 = image_positions[img1_id]
                pos2 = image_positions[img2_id]
                draw.line([pos1, pos2], fill=color, width=3)
    
    # Simpan hasil akhir
    output_path = os.path.join(folder_path, "mozaik_analisis.png")
    full_mosaic.save(output_path)
    print(f"\nVisualisasi berhasil dibuat dan disimpan di:\n{output_path}")

# --- Data Analisis Anda ---
analysis_results = [
    "Gambar 1 -> Gambar 2: Phase Correlation (Fallback)", "Gambar 1 -> Gambar 18: Phase Correlation (Fallback)", 
    "Gambar 2 -> Gambar 3: Phase Correlation (Fallback)", "Gambar 2 -> Gambar 17: Phase Correlation (Fallback)", 
    "Gambar 3 -> Gambar 4: Phase Correlation (Fallback)", "Gambar 3 -> Gambar 16: Phase Correlation (Fallback)", 
    "Gambar 4 -> Gambar 5: Phase Correlation (Fallback)", "Gambar 4 -> Gambar 15: Phase Correlation (Fallback)", 
    "Gambar 5 -> Gambar 6: Phase Correlation (Fallback)", "Gambar 5 -> Gambar 14: Phase Correlation (Fallback)", 
    "Gambar 6 -> Gambar 7: Phase Correlation (Fallback)", "Gambar 6 -> Gambar 13: Phase Correlation (Fallback)", 
    "Gambar 7 -> Gambar 8: Phase Correlation (Fallback)", "Gambar 7 -> Gambar 12: Phase Correlation (Fallback)", 
    "Gambar 8 -> Gambar 9: Phase Correlation (Fallback)", "Gambar 8 -> Gambar 11: Phase Correlation (Fallback)", 
    "Gambar 9 -> Gambar 10: Phase Correlation (Fallback)", "Gambar 18 -> Gambar 17: Phase Correlation (Fallback)", 
    "Gambar 18 -> Gambar 19: Phase Correlation (Fallback)", "Gambar 17 -> Gambar 16: Feature Detection", 
    "Gambar 17 -> Gambar 20: Feature Detection", "Gambar 16 -> Gambar 15: Feature Detection", 
    "Gambar 16 -> Gambar 21: Feature Detection", "Gambar 15 -> Gambar 14: Phase Correlation (Fallback)", 
    "Gambar 15 -> Gambar 22: Feature Detection", "Gambar 14 -> Gambar 13: Phase Correlation (Fallback)", 
    "Gambar 14 -> Gambar 23: Phase Correlation (Fallback)", "Gambar 13 -> Gambar 12: Phase Correlation (Fallback)", 
    "Gambar 13 -> Gambar 24: Phase Correlation (Fallback)", "Gambar 12 -> Gambar 11: Phase Correlation (Fallback)", 
    "Gambar 12 -> Gambar 25: Phase Correlation (Fallback)", "Gambar 11 -> Gambar 10: Feature Detection", 
    "Gambar 11 -> Gambar 26: Phase Correlation (Fallback)", "Gambar 10 -> Gambar 27: Phase Correlation (Fallback)", 
    "Gambar 19 -> Gambar 20: Feature Detection", "Gambar 19 -> Gambar 36: Phase Correlation (Fallback)", 
    "Gambar 20 -> Gambar 21: Feature Detection", "Gambar 20 -> Gambar 35: Feature Detection", 
    "Gambar 21 -> Gambar 22: Feature Detection", "Gambar 21 -> Gambar 34: Feature Detection", 
    "Gambar 22 -> Gambar 23: Feature Detection", "Gambar 22 -> Gambar 33: Phase Correlation (Fallback)", 
    "Gambar 23 -> Gambar 24: Phase Correlation (Fallback)", "Gambar 23 -> Gambar 32: Phase Correlation (Fallback)", 
    "Gambar 24 -> Gambar 25: Phase Correlation (Fallback)", "Gambar 24 -> Gambar 31: Phase Correlation (Fallback)", 
    "Gambar 25 -> Gambar 26: Phase Correlation (Fallback)", "Gambar 25 -> Gambar 30: Phase Correlation (Fallback)", 
    "Gambar 26 -> Gambar 27: Phase Correlation (Fallback)", "Gambar 26 -> Gambar 29: Phase Correlation (Fallback)", 
    "Gambar 27 -> Gambar 28: Phase Correlation (Fallback)", "Gambar 36 -> Gambar 35: Phase Correlation (Fallback)", 
    "Gambar 35 -> Gambar 34: Feature Detection", "Gambar 34 -> Gambar 33: Phase Correlation (Fallback)", 
    "Gambar 33 -> Gambar 32: Phase Correlation (Fallback)", "Gambar 32 -> Gambar 31: Phase Correlation (Fallback)", 
    "Gambar 31 -> Gambar 30: Phase Correlation (Fallback)", "Gambar 30 -> Gambar 29: Phase Correlation (Fallback)", 
    "Gambar 29 -> Gambar 28: Phase Correlation (Fallback)"
]

# --- PENTING: Ganti path folder di bawah ini dengan lokasi gambar Anda ---
folder_path = r"D:\Stitch\bilinear_stitching_20250915_152529"
visualize_analysis(folder_path, analysis_results)