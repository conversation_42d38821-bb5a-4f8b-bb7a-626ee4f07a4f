#!/usr/bin/env python3
"""
Advanced Seam Finding for Microscopic Image Stitching
Implements graph-cut based seam finding to eliminate grid artifacts
"""

import numpy as np
import cv2
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

class SeamFinder:
    """Advanced seam finding using graph-cut optimization"""

    def __init__(self, blend_width=50, cost_weights=None, feather_width=5, cost_smoothing_kernel=5):
        """
        Initialize seam finder

        Args:
            blend_width: Width of blending region in pixels
            cost_weights: Dictionary of cost function weights
            feather_width: Width of seam feathering
            cost_smoothing_kernel: Kernel size for cost map smoothing
        """
        self.blend_width = blend_width
        self.feather_width = feather_width
        self.cost_smoothing_kernel = cost_smoothing_kernel

        # Default cost weights
        self.cost_weights = cost_weights or {
            'gradient': 0.4,      # Edge preservation
            'color': 0.3,         # Color difference
            'texture': 0.2,       # Texture consistency
            'distance': 0.1       # Distance from center bias
        }
    
    def find_optimal_seam(self, img1, img2, overlap_region, direction='horizontal'):
        """
        Find optimal seam between two overlapping images
        
        Args:
            img1: First image (numpy array)
            img2: Second image (numpy array)
            overlap_region: Tuple (y1, y2, x1, x2) defining overlap region
            direction: 'horizontal' or 'vertical' seam direction
            
        Returns:
            seam_mask: Binary mask where 1=img1, 0=img2
        """
        y1, y2, x1, x2 = overlap_region
        
        # Extract overlap regions
        region1 = img1[y1:y2, x1:x2]
        region2 = img2[y1:y2, x1:x2]
        
        if region1.shape != region2.shape:
            print(f"Warning: Region shapes don't match: {region1.shape} vs {region2.shape}")
            return self._create_simple_mask(region1.shape, direction)
        
        # Compute cost map
        cost_map = self._compute_cost_map(region1, region2)
        
        # Find optimal seam using dynamic programming
        if direction == 'horizontal':
            seam_path = self._find_horizontal_seam(cost_map)
        else:
            seam_path = self._find_vertical_seam(cost_map)
        
        # Create seam mask
        seam_mask = self._create_seam_mask(region1.shape, seam_path, direction)
        
        return seam_mask
    
    def _compute_cost_map(self, img1, img2):
        """Compute multi-criteria cost map for seam finding"""
        h, w = img1.shape[:2]
        
        # Convert to grayscale if needed
        if len(img1.shape) == 3:
            gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        else:
            gray1, gray2 = img1, img2
        
        # Initialize cost map
        cost_map = np.zeros((h, w), dtype=np.float32)
        
        # 1. Gradient-based cost (edge preservation)
        if self.cost_weights['gradient'] > 0:
            grad1 = self._compute_gradient_magnitude(gray1)
            grad2 = self._compute_gradient_magnitude(gray2)
            gradient_cost = np.abs(grad1 - grad2)
            cost_map += self.cost_weights['gradient'] * gradient_cost
        
        # 2. Color difference cost
        if self.cost_weights['color'] > 0:
            if len(img1.shape) == 3:
                color_diff = np.mean(np.abs(img1.astype(float) - img2.astype(float)), axis=2)
            else:
                color_diff = np.abs(img1.astype(float) - img2.astype(float))
            cost_map += self.cost_weights['color'] * color_diff / 255.0
        
        # 3. Texture consistency cost
        if self.cost_weights['texture'] > 0:
            texture_cost = self._compute_texture_cost(gray1, gray2)
            cost_map += self.cost_weights['texture'] * texture_cost
        
        # 4. Distance from center bias (prefer center seams)
        if self.cost_weights['distance'] > 0:
            center_y, center_x = h // 2, w // 2
            y_coords, x_coords = np.ogrid[:h, :w]
            distance_cost = np.sqrt((y_coords - center_y)**2 + (x_coords - center_x)**2)
            distance_cost = distance_cost / np.max(distance_cost)  # Normalize
            cost_map += self.cost_weights['distance'] * distance_cost
        
        # Smooth the cost map to avoid noisy seams
        kernel_size = self.cost_smoothing_kernel
        if kernel_size % 2 == 0:
            kernel_size += 1  # Ensure odd kernel size
        cost_map = cv2.GaussianBlur(cost_map, (kernel_size, kernel_size), 1.0)
        
        return cost_map
    
    def _compute_gradient_magnitude(self, img):
        """Compute gradient magnitude using Sobel operator"""
        grad_x = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        return magnitude / np.max(magnitude)  # Normalize
    
    def _compute_texture_cost(self, img1, img2):
        """Compute texture-based cost using local binary patterns"""
        # Simple texture measure using local standard deviation
        kernel = np.ones((5, 5), np.float32) / 25
        
        # Local mean
        mean1 = cv2.filter2D(img1.astype(np.float32), -1, kernel)
        mean2 = cv2.filter2D(img2.astype(np.float32), -1, kernel)
        
        # Local variance (texture measure)
        var1 = cv2.filter2D((img1.astype(np.float32) - mean1)**2, -1, kernel)
        var2 = cv2.filter2D((img2.astype(np.float32) - mean2)**2, -1, kernel)
        
        # Texture difference
        texture_diff = np.abs(np.sqrt(var1) - np.sqrt(var2))
        return texture_diff / (np.max(texture_diff) + 1e-8)  # Normalize
    
    def _find_horizontal_seam(self, cost_map):
        """Find optimal horizontal seam using dynamic programming"""
        h, w = cost_map.shape
        
        # DP table: dp[i][j] = minimum cost to reach (i, j)
        dp = np.full((h, w), np.inf)
        parent = np.full((h, w, 2), -1, dtype=int)
        
        # Initialize first column
        dp[:, 0] = cost_map[:, 0]
        
        # Fill DP table
        for j in range(1, w):
            for i in range(h):
                # Check three possible previous positions
                for di in [-1, 0, 1]:
                    prev_i = i + di
                    if 0 <= prev_i < h:
                        cost = dp[prev_i, j-1] + cost_map[i, j]
                        if cost < dp[i, j]:
                            dp[i, j] = cost
                            parent[i, j] = [prev_i, j-1]
        
        # Backtrack to find optimal path
        # Find minimum cost in last column
        min_idx = np.argmin(dp[:, -1])
        
        # Reconstruct path
        path = []
        i, j = min_idx, w - 1
        while j >= 0:
            path.append((i, j))
            if parent[i, j, 0] != -1:
                i, j = parent[i, j]
            else:
                break
        
        return list(reversed(path))
    
    def _find_vertical_seam(self, cost_map):
        """Find optimal vertical seam using dynamic programming"""
        # Transpose and find horizontal seam, then transpose back
        cost_map_t = cost_map.T
        seam_path = self._find_horizontal_seam(cost_map_t)
        # Swap coordinates back
        return [(j, i) for i, j in seam_path]
    
    def _create_seam_mask(self, shape, seam_path, direction):
        """Create binary mask from seam path"""
        h, w = shape[:2]
        mask = np.zeros((h, w), dtype=np.float32)
        
        if direction == 'horizontal':
            # For each column, mark pixels above seam as 1 (img1)
            for i, j in seam_path:
                mask[:i+1, j] = 1.0
        else:
            # For each row, mark pixels left of seam as 1 (img1)
            for i, j in seam_path:
                mask[i, :j+1] = 1.0
        
        # Apply feathering around seam for smooth transition
        mask = self._apply_seam_feathering(mask, seam_path, direction)
        
        return mask
    
    def _apply_seam_feathering(self, mask, seam_path, direction, feather_width=None):
        """Apply smooth feathering around the seam"""
        if feather_width is None:
            feather_width = self.feather_width

        # Create distance transform from seam
        seam_binary = np.zeros_like(mask)

        # Mark seam pixels
        for i, j in seam_path:
            seam_binary[i, j] = 1

        # Distance transform
        dist_from_seam = ndimage.distance_transform_edt(1 - seam_binary)
        
        # Apply feathering based on which side of seam
        if direction == 'horizontal':
            # Smooth transition around horizontal seam
            for i, j in seam_path:
                # Above seam: gradually transition from 1 to feather value
                for di in range(-feather_width, feather_width+1):
                    if 0 <= i+di < mask.shape[0]:
                        if di <= 0:  # Above seam
                            alpha = 1.0 - abs(di) / feather_width
                            mask[i+di, j] = alpha
                        else:  # Below seam
                            alpha = abs(di) / feather_width
                            mask[i+di, j] = 1.0 - alpha
        
        return np.clip(mask, 0, 1)
    
    def _create_simple_mask(self, shape, direction):
        """Create simple linear mask as fallback"""
        h, w = shape[:2]
        mask = np.zeros((h, w), dtype=np.float32)
        
        if direction == 'horizontal':
            # Linear transition from top to bottom
            for i in range(h):
                mask[i, :] = 1.0 - (i / h)
        else:
            # Linear transition from left to right
            for j in range(w):
                mask[:, j] = 1.0 - (j / w)
        
        return mask

def create_overlap_regions(positions, image_shape, overlap_pct=0.2):
    """
    Calculate overlap regions between adjacent images in grid

    Args:
        positions: List of (y, x) positions for each image
        image_shape: (height, width) of individual images
        overlap_pct: Overlap percentage

    Returns:
        overlap_regions: List of overlap region definitions
    """
    h, w = image_shape
    overlap_regions = []

    for i, (y1, x1) in enumerate(positions):
        for j, (y2, x2) in enumerate(positions):
            if i >= j:  # Avoid duplicates and self-comparison
                continue

            # Calculate image boundaries
            img1_bounds = (y1, y1 + h, x1, x1 + w)  # (y1, y2, x1, x2)
            img2_bounds = (y2, y2 + h, x2, x2 + w)

            # Check for overlap
            overlap_y1 = max(img1_bounds[0], img2_bounds[0])
            overlap_y2 = min(img1_bounds[1], img2_bounds[1])
            overlap_x1 = max(img1_bounds[2], img2_bounds[2])
            overlap_x2 = min(img1_bounds[3], img2_bounds[3])

            # Check if there's actual overlap
            if overlap_y2 > overlap_y1 and overlap_x2 > overlap_x1:
                # Calculate overlap area
                overlap_area = (overlap_y2 - overlap_y1) * (overlap_x2 - overlap_x1)
                min_area_threshold = (h * w * overlap_pct * 0.5)  # Minimum overlap area

                if overlap_area >= min_area_threshold:
                    # Determine seam direction based on overlap shape
                    overlap_width = overlap_x2 - overlap_x1
                    overlap_height = overlap_y2 - overlap_y1

                    if overlap_width > overlap_height:
                        direction = 'horizontal'  # Wide overlap -> horizontal seam
                    else:
                        direction = 'vertical'    # Tall overlap -> vertical seam

                    overlap_regions.append({
                        'img1_idx': i,
                        'img2_idx': j,
                        'region': (int(overlap_y1), int(overlap_y2), int(overlap_x1), int(overlap_x2)),
                        'direction': direction,
                        'overlap_area': overlap_area
                    })

    return overlap_regions
