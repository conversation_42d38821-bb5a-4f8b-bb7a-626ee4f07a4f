#!/usr/bin/env python3
"""
Configuration file for Seam Finding Parameters
Adjust these parameters to improve stitching quality
"""

class SeamConfig:
    """Configuration class for seam finding parameters"""
    
    def __init__(self):
        # =================================================================
        # 🎯 SEAM FINDING PARAMETERS - ADJUST THESE FOR BETTER RESULTS
        # =================================================================
        
        # --- Blending Parameters ---
        self.blend_width = 80  # Default: 50
        # 📝 Effect: Wider blending creates smoother transitions but may blur details
        # 🔧 Increase (60-120) for: Smoother grid elimination, less sharp transitions
        # 🔧 Decrease (20-40) for: Sharper details, but may show more grid artifacts
        
        # --- Cost Function Weights ---
        # These control what the seam finder prioritizes when finding optimal seams
        self.cost_weights = {
            'gradient': 0.5,    # Default: 0.4 - Edge preservation
            'color': 0.2,       # Default: 0.3 - Color difference
            'texture': 0.2,     # Default: 0.2 - Texture consistency  
            'distance': 0.1     # Default: 0.1 - Distance from center bias
        }
        
        # 📝 Gradient Weight (0.0-1.0):
        # 🔧 Increase (0.5-0.7) for: Better edge preservation, avoid cutting through objects
        # 🔧 Decrease (0.1-0.3) for: More flexible seam placement, may cut through edges
        
        # 📝 Color Weight (0.0-1.0):
        # 🔧 Increase (0.4-0.6) for: Better color matching, avoid color discontinuities
        # 🔧 Decrease (0.1-0.2) for: Less emphasis on color, more on structure
        
        # 📝 Texture Weight (0.0-1.0):
        # 🔧 Increase (0.3-0.5) for: Better texture consistency, smoother backgrounds
        # 🔧 Decrease (0.0-0.1) for: Less texture consideration, faster processing
        
        # 📝 Distance Weight (0.0-0.3):
        # 🔧 Increase (0.2-0.3) for: Prefer center seams, more predictable placement
        # 🔧 Decrease (0.0-0.05) for: More flexible seam placement based on content
        
        # --- Seam Smoothing Parameters ---
        self.seam_feather_width = 8  # Default: 5
        # 📝 Effect: Controls how smooth the transition around seams is
        # 🔧 Increase (10-15) for: Smoother seam transitions, less visible seams
        # 🔧 Decrease (3-5) for: Sharper seam boundaries, more precise cuts
        
        self.cost_smoothing_kernel = 7  # Default: 5
        # 📝 Effect: Smooths the cost map to avoid noisy seam paths
        # 🔧 Increase (9-13) for: Smoother, more stable seam paths
        # 🔧 Decrease (3-5) for: More detailed seam following, may be noisier
        
        # --- Advanced Parameters ---
        self.min_overlap_threshold = 0.5  # Default: 0.5
        # 📝 Effect: Minimum overlap area ratio to process seam finding
        # 🔧 Increase (0.7-0.9) for: Only process significant overlaps
        # 🔧 Decrease (0.2-0.4) for: Process even small overlaps
        
        self.distance_transform_sigma = 1.0  # Default: 1.0
        # 📝 Effect: Controls distance-based weight falloff
        # 🔧 Increase (1.5-2.5) for: Gentler weight transitions
        # 🔧 Decrease (0.5-0.8) for: Sharper weight transitions
        
        # =================================================================
        # 🎨 BLENDING PARAMETERS
        # =================================================================
        
        self.use_multiband_blending = False  # Default: False
        # 📝 Effect: Use Laplacian pyramid blending for better quality
        # 🔧 Set True for: Higher quality blending, slower processing
        # 🔧 Set False for: Faster processing, simpler blending
        
        self.pyramid_levels = 4  # Default: 4 (only used if multiband_blending=True)
        # 📝 Effect: Number of pyramid levels for multiband blending
        # 🔧 Increase (5-6) for: Smoother blending, slower processing
        # 🔧 Decrease (2-3) for: Faster processing, may show more artifacts
        
        # =================================================================
        # 🔧 GRID ARTIFACT REMOVAL
        # =================================================================
        
        self.apply_grid_removal = True  # Default: True
        # 📝 Effect: Apply FFT-based grid artifact removal
        # 🔧 Set True for: Better grid artifact removal
        # 🔧 Set False for: Faster processing, may keep some grid artifacts
        
        self.grid_removal_strength = 0.3  # Default: 0.3
        # 📝 Effect: Strength of grid artifact removal (0.0-1.0)
        # 🔧 Increase (0.5-0.8) for: Stronger grid removal, may blur image
        # 🔧 Decrease (0.1-0.2) for: Gentler removal, may keep some artifacts
        
        # =================================================================
        # 📊 PROCESSING PARAMETERS
        # =================================================================
        
        self.debug_mode = False  # Default: False
        # 📝 Effect: Save intermediate results for debugging
        # 🔧 Set True for: Debug visualizations, slower processing
        # 🔧 Set False for: Faster processing, no debug files
        
        self.parallel_processing = False  # Default: False
        # 📝 Effect: Process multiple seams in parallel
        # 🔧 Set True for: Faster processing on multi-core systems
        # 🔧 Set False for: Sequential processing, more predictable
        
        self.memory_efficient = True  # Default: True
        # 📝 Effect: Use memory-efficient processing for large images
        # 🔧 Set True for: Lower memory usage, slightly slower
        # 🔧 Set False for: Faster processing, higher memory usage

    def get_seam_finder_config(self):
        """Get configuration for SeamFinder class"""
        return {
            'blend_width': self.blend_width,
            'cost_weights': self.cost_weights.copy(),
            'feather_width': self.seam_feather_width,
            'cost_smoothing_kernel': self.cost_smoothing_kernel
        }
    
    def print_current_config(self):
        """Print current configuration for review"""
        print("🔧 CURRENT SEAM FINDING CONFIGURATION")
        print("=" * 50)
        
        print(f"📐 Blend Width: {self.blend_width}")
        print(f"🎯 Cost Weights:")
        for key, value in self.cost_weights.items():
            print(f"   • {key.capitalize()}: {value}")
        
        print(f"🌊 Seam Feather Width: {self.seam_feather_width}")
        print(f"📊 Cost Smoothing Kernel: {self.cost_smoothing_kernel}")
        print(f"🎨 Multiband Blending: {self.use_multiband_blending}")
        print(f"🔧 Grid Removal: {self.apply_grid_removal}")
        print(f"🐛 Debug Mode: {self.debug_mode}")
        print("=" * 50)

# =================================================================
# 🎯 PRESET CONFIGURATIONS FOR DIFFERENT SCENARIOS
# =================================================================

class SeamPresets:
    """Predefined configurations for common scenarios"""
    
    @staticmethod
    def high_quality_slow():
        """High quality settings - slower processing"""
        config = SeamConfig()
        config.blend_width = 100
        config.cost_weights = {
            'gradient': 0.6,
            'color': 0.3,
            'texture': 0.1,
            'distance': 0.0
        }
        config.seam_feather_width = 12
        config.cost_smoothing_kernel = 9
        config.use_multiband_blending = True
        config.pyramid_levels = 5
        config.grid_removal_strength = 0.5
        return config
    
    @staticmethod
    def balanced():
        """Balanced quality and speed"""
        config = SeamConfig()
        config.blend_width = 60
        config.cost_weights = {
            'gradient': 0.5,
            'color': 0.3,
            'texture': 0.15,
            'distance': 0.05
        }
        config.seam_feather_width = 8
        config.cost_smoothing_kernel = 7
        config.grid_removal_strength = 0.3
        return config
    
    @staticmethod
    def fast_processing():
        """Fast processing - lower quality"""
        config = SeamConfig()
        config.blend_width = 40
        config.cost_weights = {
            'gradient': 0.4,
            'color': 0.4,
            'texture': 0.1,
            'distance': 0.1
        }
        config.seam_feather_width = 5
        config.cost_smoothing_kernel = 5
        config.use_multiband_blending = False
        config.apply_grid_removal = False
        config.parallel_processing = True
        return config
    
    @staticmethod
    def grid_elimination_focus():
        """Focused on eliminating grid artifacts"""
        config = SeamConfig()
        config.blend_width = 120
        config.cost_weights = {
            'gradient': 0.3,
            'color': 0.2,
            'texture': 0.4,
            'distance': 0.1
        }
        config.seam_feather_width = 15
        config.cost_smoothing_kernel = 11
        config.apply_grid_removal = True
        config.grid_removal_strength = 0.6
        config.use_multiband_blending = True
        return config
    
    @staticmethod
    def low_feature_images():
        """Optimized for images with few features"""
        config = SeamConfig()
        config.blend_width = 80
        config.cost_weights = {
            'gradient': 0.2,
            'color': 0.5,
            'texture': 0.2,
            'distance': 0.1
        }
        config.seam_feather_width = 10
        config.min_overlap_threshold = 0.3
        return config

# =================================================================
# 💡 USAGE EXAMPLES
# =================================================================

def example_usage():
    """Example of how to use different configurations"""
    
    print("💡 SEAM CONFIGURATION USAGE EXAMPLES")
    print("=" * 50)
    
    # Default configuration
    print("1. Default Configuration:")
    default_config = SeamConfig()
    default_config.print_current_config()
    
    print("\n2. High Quality Preset:")
    hq_config = SeamPresets.high_quality_slow()
    hq_config.print_current_config()
    
    print("\n3. Grid Elimination Preset:")
    grid_config = SeamPresets.grid_elimination_focus()
    grid_config.print_current_config()

if __name__ == "__main__":
    example_usage()
