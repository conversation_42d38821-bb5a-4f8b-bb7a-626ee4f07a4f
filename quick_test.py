#!/usr/bin/env python3
"""Quick test of GUI functionality with existing sample tiles"""

from stitching_gui import StitchingGUI
import tkinter as tk
import os

def main():
    print("🧪 Quick GUI Test with Sample Tiles")
    print("=" * 40)
    
    # Test with existing sample tiles
    root = tk.Tk()
    root.withdraw()  # Hide window
    
    gui = StitchingGUI(root)
    gui.selected_folder = 'sample_tiles'
    
    print('🔍 Testing file analysis...')
    gui._analyze_files_thread()
    print(f'   Found {len(gui.tiles)} tiles')
    
    if len(gui.tiles) > 0:
        print('🔗 Testing matches graph generation...')
        matches_info = gui._generate_simple_adjacency_graph()
        print(f'   Generated {len(matches_info)} connections')
        
        print('\n📊 Sample connections (matches graph format):')
        for i, match in enumerate(matches_info[:5]):  # Show first 5
            line = f'   "{match["tile1"]}" -- "{match["tile2"]}"[label="Nm={match["matches"]}, Ni={match["good_matches"]}, C={match["quality"]:.5f}"];'
            print(line)
        
        if len(matches_info) > 5:
            print(f'   ... and {len(matches_info) - 5} more connections')
        
        print('\n✅ GUI functionality test completed successfully!')
        print('\n🎯 To use the full GUI:')
        print('   1. Run: python run_stitching_gui.py')
        print('   2. Select the "sample_tiles" folder')
        print('   3. Follow the workflow in the GUI')
        
    else:
        print('❌ No tiles found in sample_tiles folder')
        print('   Run: python run_stitching_gui.py --create-sample')
    
    root.destroy()

if __name__ == "__main__":
    main()
