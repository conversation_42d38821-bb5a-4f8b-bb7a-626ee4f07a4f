#!/usr/bin/env python3
"""
Test script for Enhanced Image Stitching Pipeline
Tests the comprehensive pipeline with sample images
"""

import os
import sys
import numpy as np
import cv2
import time

def test_imports():
    """Test if all required modules are available"""
    print("🔍 Testing imports...")
    
    required_modules = [
        ('numpy', 'np'),
        ('cv2', 'cv2'),
        ('skimage.io', 'skimage.io'),
        ('skimage.exposure', 'exposure'),
        ('skimage.registration', 'registration'),
        ('scipy.spatial.distance', 'cdist'),
        ('scipy.optimize', 'least_squares'),
        ('scipy.ndimage', 'ndimage'),
        ('collections', 'defaultdict')
    ]
    
    failed_imports = []
    
    for module_name, import_name in required_modules:
        try:
            if module_name == 'numpy':
                import numpy as np
                print(f"✅ {module_name} imported successfully (version: {np.__version__})")
            elif module_name == 'cv2':
                import cv2
                print(f"✅ {module_name} imported successfully (version: {cv2.__version__})")
            elif module_name == 'skimage.io':
                import skimage.io
                print(f"✅ {module_name} imported successfully")
            elif module_name == 'skimage.exposure':
                from skimage import exposure
                print(f"✅ {module_name} imported successfully")
            elif module_name == 'skimage.registration':
                from skimage.registration import phase_cross_correlation
                print(f"✅ {module_name} imported successfully")
            elif module_name == 'scipy.spatial.distance':
                from scipy.spatial.distance import cdist
                print(f"✅ {module_name} imported successfully")
            elif module_name == 'scipy.optimize':
                from scipy.optimize import least_squares
                print(f"✅ {module_name} imported successfully")
            elif module_name == 'scipy.ndimage':
                from scipy.ndimage import distance_transform_edt
                print(f"✅ {module_name} imported successfully")
            elif module_name == 'collections':
                from collections import defaultdict
                print(f"✅ {module_name} imported successfully")
        except ImportError as e:
            print(f"❌ {module_name} import failed: {e}")
            failed_imports.append(module_name)
    
    return len(failed_imports) == 0

def test_feature_detectors():
    """Test feature detectors"""
    print("\n🔍 Testing feature detectors...")
    
    try:
        # Test ORB detector
        orb = cv2.ORB_create(nfeatures=100)
        print("✅ ORB detector created successfully")
        
        # Test SIFT detector (if available)
        try:
            sift = cv2.SIFT_create(nfeatures=100)
            print("✅ SIFT detector created successfully")
        except Exception as e:
            print(f"⚠️  SIFT detector not available: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Feature detector test failed: {e}")
        return False

def test_sample_images():
    """Test with sample images in current directory"""
    print("\n📁 Testing sample images...")
    
    # Look for images in current directory
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.tif', '*.tiff']
    image_files = []
    
    import glob
    for ext in image_extensions:
        image_files.extend(glob.glob(ext))
    
    if len(image_files) < 2:
        print(f"❌ Need at least 2 images for testing. Found: {len(image_files)}")
        print("Available images:", image_files)
        return False, None
    
    print(f"✅ Found {len(image_files)} images for testing")
    for i, img_file in enumerate(image_files[:5]):  # Show first 5
        print(f"  {i+1}. {img_file}")
    
    if len(image_files) > 5:
        print(f"  ... and {len(image_files) - 5} more")
    
    return True, image_files

def test_enhanced_pipeline():
    """Test the enhanced stitching pipeline"""
    print("\n🧪 Testing Enhanced Image Stitching Pipeline...")
    
    try:
        # Import the enhanced stitcher
        from proper_image_stitching import EnhancedImageStitcher
        
        # Create stitcher instance
        stitcher = EnhancedImageStitcher()
        print("✅ EnhancedImageStitcher created successfully")
        
        # Test with current directory
        current_dir = "."
        
        # Load images
        num_images = stitcher.load_images(current_dir)
        if num_images < 2:
            print("❌ Not enough images for stitching test")
            return False
        
        print(f"✅ Loaded {num_images} images successfully")
        
        # Test feature detection
        total_features = stitcher.detect_features()
        print(f"✅ Feature detection completed: {total_features} total features")
        
        # Test pairwise transforms
        successful_transforms = stitcher.compute_pairwise_transforms()
        print(f"✅ Pairwise transforms: {successful_transforms} successful")
        
        if successful_transforms > 0:
            # Test global optimization
            global_transforms = stitcher.global_bundle_adjustment()
            print(f"✅ Global optimization completed: {global_transforms} transforms")
            
            print("✅ Enhanced pipeline test completed successfully!")
            return True
        else:
            print("❌ No successful transforms - cannot complete full test")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 ENHANCED IMAGE STITCHING - COMPATIBILITY TEST")
    print("=" * 60)
    
    # Test 1: Imports
    print("\n" + "="*40)
    print("TEST 1: Module Imports")
    print("="*40)
    if not test_imports():
        print("\n❌ FAILED: Missing required modules")
        print("Please install: pip install opencv-python scikit-image scipy numpy")
        return
    
    # Test 2: Feature detectors
    print("\n" + "="*40)
    print("TEST 2: Feature Detectors")
    print("="*40)
    if not test_feature_detectors():
        print("\n❌ FAILED: Feature detectors not working")
        return
    
    # Test 3: Sample images
    print("\n" + "="*40)
    print("TEST 3: Sample Images")
    print("="*40)
    images_ok, image_files = test_sample_images()
    if not images_ok:
        print("\n❌ FAILED: Not enough sample images")
        print("Please place at least 2 images in the current directory")
        return
    
    # Test 4: Enhanced pipeline
    print("\n" + "="*40)
    print("TEST 4: Enhanced Pipeline")
    print("="*40)
    if not test_enhanced_pipeline():
        print("\n❌ FAILED: Enhanced pipeline test failed")
        return
    
    print("\n" + "="*60)
    print("✅ ALL TESTS PASSED!")
    print("="*60)
    print("🎯 Ready to run enhanced image stitching")
    print(f"📁 Sample images: {len(image_files)} found")
    print("\n🚀 To run the full enhanced stitching pipeline:")
    print("   python proper_image_stitching.py")
    print("\n💡 The enhanced pipeline includes:")
    print("   ✅ ORB feature detection with phase correlation fallback")
    print("   ✅ Global bundle adjustment for error minimization")
    print("   ✅ Advanced blending with distance-based feathering")
    print("   ✅ Automatic exposure compensation")
    print("   ✅ Seamless high-resolution output")

if __name__ == "__main__":
    main()
