#!/usr/bin/env python3
"""
Demo script for Stitching GUI
Shows how to use the GUI programmatically and demonstrates features
"""

import os
import sys
import time
import tempfile
from pathlib import Path

def create_demo_dataset():
    """Create a demo dataset for testing"""
    print("🎨 Creating demo dataset...")
    
    # Create demo folder
    demo_folder = "demo_tiles"
    if os.path.exists(demo_folder):
        import shutil
        shutil.rmtree(demo_folder)
    
    os.makedirs(demo_folder)
    
    # Import after creating folder
    import numpy as np
    import cv2
    
    # Create a 3x4 grid of tiles with realistic microscopy-like patterns
    grid_rows, grid_cols = 3, 4
    tile_size = 300
    overlap = 60  # 20% overlap
    
    for row in range(grid_rows):
        for col in range(grid_cols):
            # Create base image
            img = np.zeros((tile_size, tile_size, 3), dtype=np.uint8)
            
            # Create microscopy-like background
            # Simulate tissue/cell structure
            for y in range(tile_size):
                for x in range(tile_size):
                    # Base tissue color
                    base_r = 180 + int(30 * np.sin(x/20) * np.cos(y/25))
                    base_g = 150 + int(20 * np.sin(x/15) * np.cos(y/30))
                    base_b = 120 + int(25 * np.sin(x/18) * np.cos(y/22))
                    
                    # Add position-based variation for stitching
                    pos_r = base_r + (row * 10) + int(5 * np.sin((x + row*tile_size)/50))
                    pos_g = base_g + (col * 8) + int(5 * np.cos((y + col*tile_size)/45))
                    pos_b = base_b + ((row+col) * 6) + int(5 * np.sin((x+y)/40))
                    
                    img[y, x] = [
                        np.clip(pos_r, 0, 255),
                        np.clip(pos_g, 0, 255), 
                        np.clip(pos_b, 0, 255)
                    ]
            
            # Add cell-like structures
            num_cells = np.random.randint(8, 15)
            for _ in range(num_cells):
                center_x = np.random.randint(50, tile_size-50)
                center_y = np.random.randint(50, tile_size-50)
                radius = np.random.randint(15, 35)
                
                # Cell body
                cv2.circle(img, (center_x, center_y), radius, 
                          (200, 180, 160), -1)
                
                # Cell membrane
                cv2.circle(img, (center_x, center_y), radius, 
                          (100, 80, 60), 2)
                
                # Nucleus
                nucleus_radius = radius // 3
                cv2.circle(img, (center_x, center_y), nucleus_radius,
                          (80, 60, 120), -1)
            
            # Add some fiber-like structures for feature matching
            num_fibers = np.random.randint(3, 8)
            for _ in range(num_fibers):
                start_x = np.random.randint(0, tile_size)
                start_y = np.random.randint(0, tile_size)
                end_x = start_x + np.random.randint(-100, 100)
                end_y = start_y + np.random.randint(-100, 100)
                
                end_x = np.clip(end_x, 0, tile_size-1)
                end_y = np.clip(end_y, 0, tile_size-1)
                
                cv2.line(img, (start_x, start_y), (end_x, end_y),
                        (140, 120, 100), 3)
            
            # Add some noise for realism
            noise = np.random.randint(-15, 15, img.shape, dtype=np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            # Add subtle grid position marker (for debugging)
            cv2.putText(img, f"({row},{col})", (10, 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            # Save tile
            filename = f"tile_{row:02d}_{col:02d}.png"
            filepath = os.path.join(demo_folder, filename)
            cv2.imwrite(filepath, img)
    
    print(f"✅ Created {grid_rows}x{grid_cols} demo tiles in '{demo_folder}/'")
    return demo_folder

def demonstrate_gui_features():
    """Demonstrate GUI features programmatically"""
    print("\n🔬 Demonstrating GUI Features")
    print("=" * 50)
    
    # Create demo data
    demo_folder = create_demo_dataset()
    
    print(f"\n📁 Demo dataset created in: {demo_folder}")
    print("   Files created:")
    for file in sorted(os.listdir(demo_folder)):
        if file.endswith('.png'):
            print(f"   - {file}")
    
    print(f"\n🎯 To test the GUI:")
    print(f"1. Run: python run_stitching_gui.py")
    print(f"2. Click 'Select Folder' and choose: {os.path.abspath(demo_folder)}")
    print(f"3. Click 'Analyze Files' - should find 12 tiles")
    print(f"4. Click 'Generate Matches Graph' - will show connections")
    print(f"5. Click 'Validate Connections' - should show single component")
    print(f"6. Click 'Start Stitching' - will create seamless mosaic")
    
    # Show expected matches graph format
    print(f"\n📊 Expected Matches Graph Format:")
    print(f'   "tile_00_00.png" -- "tile_00_01.png"[label="Nm=XX, Ni=YY, C=Z.ZZZZZ"];')
    print(f'   "tile_00_01.png" -- "tile_00_02.png"[label="Nm=XX, Ni=YY, C=Z.ZZZZZ"];')
    print(f'   "tile_00_00.png" -- "tile_01_00.png"[label="Nm=XX, Ni=YY, C=Z.ZZZZZ"];')
    print(f"   ... (and more connections)")
    
    return demo_folder

def run_automated_test():
    """Run automated test of GUI functionality"""
    print("\n🤖 Running Automated GUI Test")
    print("=" * 50)
    
    try:
        from stitching_gui import StitchingGUI
        import tkinter as tk
        
        # Create demo data
        demo_folder = create_demo_dataset()
        
        # Create GUI instance
        root = tk.Tk()
        root.withdraw()  # Hide window for automated test
        
        gui = StitchingGUI(root)
        gui.selected_folder = demo_folder
        
        print("🔍 Step 1: Analyzing files...")
        gui._analyze_files_thread()
        print(f"   Found {len(gui.tiles)} tiles")
        
        print("🔗 Step 2: Generating matches graph...")
        matches_info = gui._generate_simple_adjacency_graph()
        print(f"   Generated {len(matches_info)} connections")
        
        print("📊 Step 3: Displaying matches...")
        gui._update_matches_display(matches_info)
        
        # Show some sample connections
        print("   Sample connections:")
        for i, match in enumerate(matches_info[:5]):  # Show first 5
            print(f'   "{match["tile1"]}" -- "{match["tile2"]}"[label="Nm={match["matches"]}, Ni={match["good_matches"]}, C={match["quality"]:.5f}"];')
        
        if len(matches_info) > 5:
            print(f"   ... and {len(matches_info) - 5} more connections")
        
        print("✅ Automated test completed successfully!")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Automated test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_examples():
    """Show usage examples"""
    print("\n📚 Usage Examples")
    print("=" * 50)
    
    print("🔧 Command Line Usage:")
    print("   python run_stitching_gui.py                    # Start GUI")
    print("   python run_stitching_gui.py --create-sample    # Create test data")
    print("   python run_stitching_gui.py --help             # Show help")
    print("   python demo_gui.py                             # Run this demo")
    
    print("\n🎯 GUI Workflow:")
    print("   1. Select folder with tile images")
    print("   2. Analyze files (validates naming: tile_XX_YY.png)")
    print("   3. Generate matches graph (finds adjacent connections)")
    print("   4. Validate connections (checks grid integrity)")
    print("   5. Configure parameters (overlap, method, etc.)")
    print("   6. Start stitching (creates seamless mosaic)")
    
    print("\n⚙️ Parameter Recommendations:")
    print("   - Overlap Threshold: 0.2-0.4 for most cases")
    print("   - Min Matches: 10-30 for reliable connections")
    print("   - Method: 'seam_finding' for best quality")
    
    print("\n📁 File Requirements:")
    print("   - Naming: tile_{row}_{col}.png (e.g., tile_01_02.png)")
    print("   - Format: PNG, JPG, JPEG supported")
    print("   - Layout: Regular grid with consistent overlap")

def main():
    """Main demo function"""
    print("🚀 Stitching GUI Demo")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            success = run_automated_test()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "--create-only":
            demo_folder = create_demo_dataset()
            print(f"\n✅ Demo data created in: {demo_folder}")
            print("Now run: python run_stitching_gui.py")
            sys.exit(0)
        elif sys.argv[1] == "--help":
            show_usage_examples()
            sys.exit(0)
    
    # Full demo
    demonstrate_gui_features()
    
    print("\n🎮 Interactive Options:")
    print("1. Run automated test: python demo_gui.py --test")
    print("2. Create data only: python demo_gui.py --create-only")
    print("3. Start GUI: python run_stitching_gui.py")
    print("4. Show help: python demo_gui.py --help")
    
    # Ask user what to do next
    try:
        choice = input("\n❓ Start GUI now? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            print("🚀 Starting GUI...")
            os.system("python run_stitching_gui.py")
    except KeyboardInterrupt:
        print("\n👋 Demo cancelled")

if __name__ == "__main__":
    main()
